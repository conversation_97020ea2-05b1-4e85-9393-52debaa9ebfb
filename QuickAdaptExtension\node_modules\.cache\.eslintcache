[{"E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\index.tsx": "1", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\reportWebVitals.ts": "2", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\App.tsx": "3", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\UserInfoStore.ts": "4", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\Drawer.tsx": "5", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\AuthProvider.tsx": "6", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\AccountContext.tsx": "7", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\SnackbarContext.tsx": "8", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\drawerStore.ts": "9", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\historyStore.ts": "10", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\userSession.ts": "11", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuidePopUp.tsx": "12", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuideSettings.tsx": "13", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\LogoutPopup.tsx": "14", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\ExtensionLogin.tsx": "15", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\GuideMenuOptions.tsx": "16", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\ScrapingService.ts": "17", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\UserService.ts": "18", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\SaveGuideService.tsx": "19", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\GuideListServices.tsx": "20", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\assets\\icons\\icons.ts": "21", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\UseAuth.tsx": "22", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistLauncherPreview.tsx": "23", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPopup.tsx": "24", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\UndoRedoButtons.tsx": "25", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\TrainingField.tsx": "26", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Design.tsx": "27", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\Banners.tsx": "28", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Bannerspreview\\Banner.tsx": "29", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\AnnouncementPreview.tsx": "30", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\HotspotPreview.tsx": "31", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourTemplate.tsx": "32", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\Tooltip.tsx": "33", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\stepPopup.tsx": "34", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourPreview.tsx": "35", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltipuserview.tsx": "36", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\OrganizationService.ts": "37", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\FileService.tsx": "38", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\LoginService.tsx": "39", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\utils\\historyUtils.ts": "40", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementRules.tsx": "41", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PageTrigger.tsx": "42", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\AlertPopup.tsx": "43", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\StopScrapingButton.tsx": "44", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\PopupList.tsx": "45", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\OidcConfig.ts": "46", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Imagesection.tsx": "47", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\APIService.tsx": "48", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\RTEsection.tsx": "49", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\VideoSection.tsx": "50", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Button.tsx": "51", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPreview.tsx": "52", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\HtmlSection.tsx": "53", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\CreateWithAIButton.tsx": "54", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\ModernChatWindow.tsx": "55", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCheckIcon.tsx": "56", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\LauncherSettings.tsx": "57", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ImageCarousel.tsx": "58", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\VideoPlayer.tsx": "59", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\Chekpoints.tsx": "60", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CanvasSettings.tsx": "61", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Overlay.tsx": "62", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementsSettings.tsx": "63", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Animation.tsx": "64", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CustomCss.tsx": "65", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCanvasSettings.tsx": "66", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\TitleSubTitle.tsx": "67", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\PageInteraction.tsx": "68", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ButtonSettings.tsx": "69", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageSectionField.tsx": "70", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\hotspot\\HotspotSettings.tsx": "71", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\BannerStepPreview.tsx": "72", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\TooltipBody.tsx": "73", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltips.tsx": "74", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\designFields\\TooltipCanvasSettings.tsx": "75", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\CloneGuidePopUp.tsx": "76", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\TimeZoneConversion.tsx": "77", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\SelectImageFromApplication.tsx": "78", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\SpeechRecognitionService.ts": "79", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\AIService.ts": "80", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\DraggableCheckpoint.tsx": "81", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointEditPopup.tsx": "82", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointAddPopup.tsx": "83", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageProperties.tsx": "84", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageGalleryPopup.tsx": "85", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\Buttons.tsx": "86", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ImageSection.tsx": "87", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\RTE\\RTESection.tsx": "88", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ButtonSetting.tsx": "89", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\UserRoleService.ts": "90", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\i18n.ts": "91", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\contexts\\TranslationContext.tsx": "92", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\LanguageSelector.tsx": "93", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\LanguageService.ts": "94", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\AgentTraining.tsx": "95", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\AgentAdditionalContextPopup.tsx": "96"}, {"size": 604, "mtime": 1748929949904, "results": "97", "hashOfConfig": "98"}, {"size": 440, "mtime": 1748929949904, "results": "99", "hashOfConfig": "98"}, {"size": 2871, "mtime": 1753868495271, "results": "100", "hashOfConfig": "98"}, {"size": 3344, "mtime": 1753158986544, "results": "101", "hashOfConfig": "98"}, {"size": 248995, "mtime": 1753878479732, "results": "102", "hashOfConfig": "98"}, {"size": 6890, "mtime": 1753072098544, "results": "103", "hashOfConfig": "98"}, {"size": 3102, "mtime": 1753781192983, "results": "104", "hashOfConfig": "98"}, {"size": 3112, "mtime": 1753072087888, "results": "105", "hashOfConfig": "98"}, {"size": 397807, "mtime": 1753874458455, "results": "106", "hashOfConfig": "98"}, {"size": 3927, "mtime": 1748930023076, "results": "107", "hashOfConfig": "98"}, {"size": 6144, "mtime": 1749531263982, "results": "108", "hashOfConfig": "98"}, {"size": 42316, "mtime": 1753871993515, "results": "109", "hashOfConfig": "98"}, {"size": 5077, "mtime": 1753868495318, "results": "110", "hashOfConfig": "98"}, {"size": 3731, "mtime": 1753270938486, "results": "111", "hashOfConfig": "98"}, {"size": 13882, "mtime": 1753871883579, "results": "112", "hashOfConfig": "98"}, {"size": 13281, "mtime": 1753868495333, "results": "113", "hashOfConfig": "98"}, {"size": 56751, "mtime": 1753781192998, "results": "114", "hashOfConfig": "98"}, {"size": 1898, "mtime": 1748930023076, "results": "115", "hashOfConfig": "98"}, {"size": 1954, "mtime": 1751432283612, "results": "116", "hashOfConfig": "98"}, {"size": 9087, "mtime": 1753158986542, "results": "117", "hashOfConfig": "98"}, {"size": 299011, "mtime": 1753868495286, "results": "118", "hashOfConfig": "98"}, {"size": 193, "mtime": 1748929949654, "results": "119", "hashOfConfig": "98"}, {"size": 9097, "mtime": 1753158986506, "results": "120", "hashOfConfig": "98"}, {"size": 30700, "mtime": 1753072098544, "results": "121", "hashOfConfig": "98"}, {"size": 3012, "mtime": 1753868495302, "results": "122", "hashOfConfig": "98"}, {"size": 2606, "mtime": 1753072098528, "results": "123", "hashOfConfig": "98"}, {"size": 33243, "mtime": 1753868495318, "results": "124", "hashOfConfig": "98"}, {"size": 23362, "mtime": 1753871883564, "results": "125", "hashOfConfig": "98"}, {"size": 13556, "mtime": 1753432486901, "results": "126", "hashOfConfig": "98"}, {"size": 27143, "mtime": 1753871883533, "results": "127", "hashOfConfig": "98"}, {"size": 49705, "mtime": 1753432487861, "results": "128", "hashOfConfig": "98"}, {"size": 7599, "mtime": 1753072087888, "results": "129", "hashOfConfig": "98"}, {"size": 32522, "mtime": 1753871883548, "results": "130", "hashOfConfig": "98"}, {"size": 11669, "mtime": 1753868495333, "results": "131", "hashOfConfig": "98"}, {"size": 24200, "mtime": 1751432283612, "results": "132", "hashOfConfig": "98"}, {"size": 4880, "mtime": 1750229130169, "results": "133", "hashOfConfig": "98"}, {"size": 9238, "mtime": 1748930023061, "results": "134", "hashOfConfig": "98"}, {"size": 1297, "mtime": 1748930023061, "results": "135", "hashOfConfig": "98"}, {"size": 1248, "mtime": 1748929949920, "results": "136", "hashOfConfig": "98"}, {"size": 14238, "mtime": 1748930023076, "results": "137", "hashOfConfig": "98"}, {"size": 2997, "mtime": 1753072087872, "results": "138", "hashOfConfig": "98"}, {"size": 3285, "mtime": 1753868495318, "results": "139", "hashOfConfig": "98"}, {"size": 2749, "mtime": 1753868495302, "results": "140", "hashOfConfig": "98"}, {"size": 2052, "mtime": 1753781192967, "results": "141", "hashOfConfig": "98"}, {"size": 20044, "mtime": 1753781192983, "results": "142", "hashOfConfig": "98"}, {"size": 743, "mtime": 1748929949654, "results": "143", "hashOfConfig": "98"}, {"size": 25466, "mtime": 1753868495318, "results": "144", "hashOfConfig": "98"}, {"size": 2608, "mtime": 1748930023061, "results": "145", "hashOfConfig": "98"}, {"size": 41121, "mtime": 1753871814189, "results": "146", "hashOfConfig": "98"}, {"size": 7772, "mtime": 1753072087872, "results": "147", "hashOfConfig": "98"}, {"size": 16105, "mtime": 1753868495318, "results": "148", "hashOfConfig": "98"}, {"size": 29119, "mtime": 1753158986510, "results": "149", "hashOfConfig": "98"}, {"size": 6245, "mtime": 1748929949857, "results": "150", "hashOfConfig": "98"}, {"size": 2034, "mtime": 1753072098528, "results": "151", "hashOfConfig": "98"}, {"size": 29744, "mtime": 1753158986497, "results": "152", "hashOfConfig": "98"}, {"size": 1962, "mtime": 1748929949654, "results": "153", "hashOfConfig": "98"}, {"size": 27254, "mtime": 1753871883548, "results": "154", "hashOfConfig": "98"}, {"size": 2423, "mtime": 1753781192967, "results": "155", "hashOfConfig": "98"}, {"size": 702, "mtime": 1753072087841, "results": "156", "hashOfConfig": "98"}, {"size": 13889, "mtime": 1753072087841, "results": "157", "hashOfConfig": "98"}, {"size": 19214, "mtime": 1753871883564, "results": "158", "hashOfConfig": "98"}, {"size": 6625, "mtime": 1753072087872, "results": "159", "hashOfConfig": "98"}, {"size": 20321, "mtime": 1753072087872, "results": "160", "hashOfConfig": "98"}, {"size": 3236, "mtime": 1748929949779, "results": "161", "hashOfConfig": "98"}, {"size": 2848, "mtime": 1748929949811, "results": "162", "hashOfConfig": "98"}, {"size": 15264, "mtime": 1753871883548, "results": "163", "hashOfConfig": "98"}, {"size": 15261, "mtime": 1753158986514, "results": "164", "hashOfConfig": "98"}, {"size": 11208, "mtime": 1753432486451, "results": "165", "hashOfConfig": "98"}, {"size": 17396, "mtime": 1753781192983, "results": "166", "hashOfConfig": "98"}, {"size": 8476, "mtime": 1753072087856, "results": "167", "hashOfConfig": "98"}, {"size": 15571, "mtime": 1753072098560, "results": "168", "hashOfConfig": "98"}, {"size": 16126, "mtime": 1753432481019, "results": "169", "hashOfConfig": "98"}, {"size": 33829, "mtime": 1753871883548, "results": "170", "hashOfConfig": "98"}, {"size": 61422, "mtime": 1753871883533, "results": "171", "hashOfConfig": "98"}, {"size": 26698, "mtime": 1753432486147, "results": "172", "hashOfConfig": "98"}, {"size": 5258, "mtime": 1753072087872, "results": "173", "hashOfConfig": "98"}, {"size": 883, "mtime": 1748929949889, "results": "174", "hashOfConfig": "98"}, {"size": 3117, "mtime": 1753868495302, "results": "175", "hashOfConfig": "98"}, {"size": 7943, "mtime": 1748930023061, "results": "176", "hashOfConfig": "98"}, {"size": 1092, "mtime": 1753781192983, "results": "177", "hashOfConfig": "98"}, {"size": 5504, "mtime": 1753072087841, "results": "178", "hashOfConfig": "98"}, {"size": 33137, "mtime": 1753868495302, "results": "179", "hashOfConfig": "98"}, {"size": 37236, "mtime": 1753868495302, "results": "180", "hashOfConfig": "98"}, {"size": 2931, "mtime": 1749010760558, "results": "181", "hashOfConfig": "98"}, {"size": 2669, "mtime": 1748929949748, "results": "182", "hashOfConfig": "98"}, {"size": 17277, "mtime": 1753868495286, "results": "183", "hashOfConfig": "98"}, {"size": 27631, "mtime": 1753868495286, "results": "184", "hashOfConfig": "98"}, {"size": 18982, "mtime": 1753871826392, "results": "185", "hashOfConfig": "98"}, {"size": 15667, "mtime": 1753781192967, "results": "186", "hashOfConfig": "98"}, {"size": 677, "mtime": 1753072098575, "results": "187", "hashOfConfig": "98"}, {"size": 6886, "mtime": 1753158986540, "results": "188", "hashOfConfig": "98"}, {"size": 7158, "mtime": 1753158986535, "results": "189", "hashOfConfig": "98"}, {"size": 10166, "mtime": 1753868495302, "results": "190", "hashOfConfig": "98"}, {"size": 1211, "mtime": 1753158986538, "results": "191", "hashOfConfig": "98"}, {"size": 3836, "mtime": 1753781192967, "results": "192", "hashOfConfig": "98"}, {"size": 3021, "mtime": 1753781192967, "results": "193", "hashOfConfig": "98"}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1c51j82", {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 224, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 40, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 61, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 25, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 50, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 54, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 57, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 33, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 31, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 28, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 62, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 51, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 29, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 49, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 62, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 32, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\index.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\reportWebVitals.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\App.tsx", ["482", "483", "484"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\UserInfoStore.ts", ["485"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\Drawer.tsx", ["486", "487", "488", "489", "490", "491", "492", "493", "494", "495", "496", "497", "498", "499", "500", "501", "502", "503", "504", "505", "506", "507", "508", "509", "510", "511", "512", "513", "514", "515", "516", "517", "518", "519", "520", "521", "522", "523", "524", "525", "526", "527", "528", "529", "530", "531", "532", "533", "534", "535", "536", "537", "538", "539", "540", "541", "542", "543", "544", "545", "546", "547", "548", "549", "550", "551", "552", "553", "554", "555", "556", "557", "558", "559", "560", "561", "562", "563", "564", "565", "566", "567", "568", "569", "570", "571", "572", "573", "574", "575", "576", "577", "578", "579", "580", "581", "582", "583", "584", "585", "586", "587", "588", "589", "590", "591", "592", "593", "594", "595", "596", "597", "598", "599", "600", "601", "602", "603", "604", "605", "606", "607", "608", "609", "610", "611", "612", "613", "614", "615", "616", "617", "618", "619", "620", "621", "622", "623", "624", "625", "626", "627", "628", "629", "630", "631", "632", "633", "634", "635", "636", "637", "638", "639", "640", "641", "642", "643", "644", "645", "646", "647", "648", "649", "650", "651", "652", "653", "654", "655", "656", "657", "658", "659", "660", "661", "662", "663", "664", "665", "666", "667", "668", "669", "670", "671", "672", "673", "674", "675", "676", "677", "678", "679", "680", "681", "682", "683", "684", "685", "686", "687", "688", "689", "690", "691", "692", "693", "694", "695", "696", "697", "698", "699", "700", "701", "702", "703", "704", "705", "706", "707", "708", "709"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\AuthProvider.tsx", ["710", "711", "712", "713", "714", "715"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\AccountContext.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\SnackbarContext.tsx", ["716", "717"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\drawerStore.ts", ["718", "719", "720", "721", "722", "723", "724", "725", "726", "727", "728", "729", "730", "731", "732", "733", "734", "735", "736", "737", "738", "739"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\historyStore.ts", ["740"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\userSession.ts", ["741"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuidePopUp.tsx", ["742", "743", "744", "745", "746", "747", "748", "749", "750", "751", "752", "753", "754", "755", "756", "757", "758", "759", "760", "761", "762", "763", "764", "765", "766", "767", "768", "769", "770", "771", "772", "773", "774", "775", "776", "777", "778", "779", "780", "781"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuideSettings.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\LogoutPopup.tsx", ["782"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\ExtensionLogin.tsx", ["783", "784", "785", "786", "787", "788", "789", "790", "791", "792", "793", "794", "795", "796", "797", "798"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\GuideMenuOptions.tsx", ["799", "800", "801", "802", "803", "804", "805", "806", "807", "808", "809", "810", "811", "812", "813", "814", "815", "816", "817", "818", "819", "820", "821", "822"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\ScrapingService.ts", ["823", "824", "825", "826", "827", "828", "829", "830", "831", "832", "833", "834", "835", "836", "837"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\UserService.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\SaveGuideService.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\GuideListServices.tsx", ["838", "839", "840"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\assets\\icons\\icons.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\UseAuth.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistLauncherPreview.tsx", ["841", "842", "843", "844", "845", "846", "847", "848"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPopup.tsx", ["849", "850", "851", "852", "853", "854", "855", "856", "857", "858", "859", "860", "861", "862", "863", "864", "865", "866", "867", "868", "869", "870", "871", "872", "873", "874", "875"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\UndoRedoButtons.tsx", ["876", "877", "878", "879"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\TrainingField.tsx", ["880", "881", "882", "883", "884", "885", "886", "887", "888", "889", "890", "891", "892", "893", "894", "895"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Design.tsx", ["896", "897", "898", "899", "900", "901", "902", "903", "904", "905", "906", "907", "908", "909", "910", "911", "912", "913", "914", "915", "916", "917", "918", "919", "920", "921", "922", "923", "924", "925", "926", "927", "928", "929", "930", "931", "932", "933", "934", "935", "936", "937", "938", "939", "940", "941", "942", "943", "944", "945", "946", "947", "948", "949", "950", "951", "952", "953", "954", "955", "956"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\Banners.tsx", ["957", "958", "959", "960", "961", "962", "963", "964", "965", "966", "967", "968", "969", "970", "971", "972", "973", "974", "975", "976", "977", "978"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Bannerspreview\\Banner.tsx", ["979", "980", "981", "982", "983", "984", "985", "986", "987", "988", "989", "990", "991", "992", "993", "994", "995", "996", "997", "998", "999"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\AnnouncementPreview.tsx", ["1000", "1001", "1002", "1003", "1004", "1005", "1006", "1007", "1008", "1009", "1010", "1011", "1012", "1013", "1014", "1015", "1016", "1017", "1018", "1019", "1020", "1021", "1022", "1023", "1024"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\HotspotPreview.tsx", ["1025", "1026", "1027", "1028", "1029", "1030", "1031", "1032", "1033", "1034", "1035", "1036", "1037", "1038", "1039", "1040", "1041", "1042", "1043", "1044", "1045", "1046", "1047", "1048", "1049", "1050", "1051"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourTemplate.tsx", ["1052", "1053", "1054", "1055", "1056", "1057", "1058", "1059", "1060", "1061"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\Tooltip.tsx", ["1062", "1063", "1064", "1065", "1066", "1067", "1068", "1069", "1070", "1071", "1072", "1073", "1074", "1075", "1076", "1077", "1078", "1079", "1080", "1081", "1082", "1083", "1084", "1085", "1086", "1087", "1088", "1089", "1090", "1091", "1092", "1093", "1094", "1095", "1096", "1097", "1098", "1099", "1100", "1101", "1102", "1103", "1104", "1105", "1106", "1107", "1108", "1109", "1110", "1111"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\stepPopup.tsx", ["1112", "1113", "1114", "1115", "1116", "1117", "1118", "1119", "1120"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourPreview.tsx", ["1121", "1122", "1123", "1124", "1125", "1126", "1127", "1128", "1129", "1130", "1131", "1132", "1133", "1134", "1135", "1136", "1137", "1138", "1139", "1140", "1141"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltipuserview.tsx", ["1142", "1143", "1144", "1145", "1146", "1147"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\OrganizationService.ts", ["1148", "1149", "1150", "1151"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\FileService.tsx", ["1152", "1153"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\LoginService.tsx", ["1154", "1155", "1156"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\utils\\historyUtils.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementRules.tsx", ["1157", "1158"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PageTrigger.tsx", ["1159"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\AlertPopup.tsx", ["1160", "1161", "1162"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\StopScrapingButton.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\PopupList.tsx", ["1163", "1164", "1165", "1166", "1167", "1168", "1169", "1170", "1171", "1172", "1173", "1174", "1175", "1176"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\OidcConfig.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Imagesection.tsx", ["1177", "1178", "1179", "1180", "1181"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\APIService.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\RTEsection.tsx", ["1182", "1183", "1184", "1185", "1186", "1187", "1188", "1189"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\VideoSection.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Button.tsx", ["1190", "1191", "1192", "1193", "1194", "1195", "1196", "1197", "1198", "1199", "1200", "1201", "1202", "1203", "1204", "1205", "1206", "1207", "1208", "1209", "1210", "1211", "1212", "1213", "1214", "1215", "1216"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPreview.tsx", ["1217", "1218", "1219", "1220", "1221", "1222", "1223", "1224", "1225", "1226", "1227"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\HtmlSection.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\CreateWithAIButton.tsx", ["1228", "1229", "1230", "1231", "1232"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\ModernChatWindow.tsx", ["1233", "1234", "1235", "1236", "1237", "1238", "1239", "1240", "1241", "1242", "1243", "1244", "1245", "1246"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCheckIcon.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\LauncherSettings.tsx", ["1247", "1248", "1249", "1250", "1251", "1252", "1253", "1254", "1255", "1256", "1257", "1258", "1259", "1260", "1261", "1262", "1263", "1264", "1265", "1266", "1267", "1268", "1269", "1270", "1271", "1272", "1273", "1274", "1275", "1276", "1277", "1278", "1279", "1280", "1281", "1282", "1283", "1284", "1285", "1286", "1287", "1288", "1289", "1290", "1291", "1292", "1293", "1294", "1295", "1296", "1297", "1298", "1299", "1300"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ImageCarousel.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\VideoPlayer.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\Chekpoints.tsx", ["1301", "1302", "1303", "1304", "1305", "1306", "1307", "1308", "1309", "1310", "1311", "1312", "1313", "1314", "1315", "1316", "1317", "1318", "1319", "1320", "1321", "1322", "1323", "1324", "1325", "1326", "1327", "1328", "1329", "1330", "1331", "1332", "1333", "1334", "1335", "1336", "1337", "1338", "1339", "1340", "1341", "1342", "1343", "1344", "1345", "1346", "1347", "1348", "1349", "1350", "1351", "1352", "1353", "1354", "1355", "1356", "1357"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CanvasSettings.tsx", ["1358", "1359", "1360", "1361"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Overlay.tsx", ["1362", "1363", "1364", "1365", "1366", "1367"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementsSettings.tsx", ["1368", "1369", "1370", "1371", "1372", "1373", "1374", "1375", "1376", "1377", "1378", "1379", "1380", "1381", "1382", "1383", "1384", "1385", "1386", "1387"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Animation.tsx", ["1388", "1389", "1390"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CustomCss.tsx", ["1391", "1392"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCanvasSettings.tsx", ["1393", "1394", "1395", "1396", "1397", "1398", "1399", "1400", "1401", "1402", "1403", "1404", "1405", "1406", "1407", "1408", "1409", "1410", "1411", "1412", "1413", "1414", "1415", "1416", "1417", "1418", "1419", "1420", "1421", "1422", "1423", "1424", "1425"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\TitleSubTitle.tsx", ["1426", "1427", "1428", "1429", "1430", "1431", "1432", "1433", "1434", "1435", "1436", "1437", "1438", "1439", "1440", "1441", "1442", "1443", "1444", "1445", "1446", "1447", "1448", "1449", "1450", "1451", "1452", "1453", "1454", "1455", "1456"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\PageInteraction.tsx", ["1457", "1458", "1459", "1460", "1461", "1462", "1463", "1464", "1465", "1466", "1467", "1468", "1469", "1470", "1471", "1472", "1473", "1474", "1475", "1476", "1477", "1478", "1479", "1480", "1481", "1482", "1483", "1484"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ButtonSettings.tsx", ["1485", "1486", "1487", "1488", "1489", "1490", "1491", "1492", "1493", "1494", "1495", "1496", "1497", "1498", "1499", "1500", "1501", "1502", "1503", "1504", "1505", "1506", "1507", "1508", "1509", "1510", "1511", "1512", "1513", "1514", "1515", "1516", "1517", "1518", "1519", "1520", "1521", "1522", "1523", "1524", "1525", "1526", "1527", "1528", "1529", "1530", "1531", "1532", "1533", "1534", "1535", "1536", "1537", "1538", "1539", "1540", "1541", "1542", "1543", "1544", "1545", "1546"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageSectionField.tsx", ["1547", "1548", "1549", "1550", "1551", "1552", "1553", "1554"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\hotspot\\HotspotSettings.tsx", ["1555", "1556", "1557", "1558", "1559", "1560", "1561", "1562", "1563", "1564", "1565", "1566", "1567", "1568", "1569", "1570", "1571", "1572", "1573"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\BannerStepPreview.tsx", ["1574", "1575", "1576", "1577", "1578", "1579", "1580", "1581", "1582", "1583", "1584", "1585", "1586", "1587", "1588", "1589", "1590", "1591", "1592", "1593"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\TooltipBody.tsx", ["1594", "1595", "1596", "1597", "1598", "1599", "1600", "1601", "1602", "1603", "1604", "1605", "1606", "1607", "1608", "1609", "1610", "1611", "1612", "1613", "1614", "1615", "1616", "1617", "1618", "1619", "1620", "1621", "1622", "1623", "1624", "1625", "1626", "1627", "1628", "1629", "1630", "1631", "1632", "1633", "1634", "1635", "1636", "1637", "1638", "1639", "1640", "1641", "1642", "1643", "1644"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltips.tsx", ["1645", "1646", "1647", "1648", "1649", "1650", "1651", "1652", "1653", "1654", "1655", "1656", "1657", "1658", "1659", "1660"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\designFields\\TooltipCanvasSettings.tsx", ["1661", "1662", "1663", "1664", "1665", "1666", "1667", "1668", "1669", "1670", "1671", "1672", "1673", "1674", "1675", "1676", "1677"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\CloneGuidePopUp.tsx", ["1678", "1679"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\TimeZoneConversion.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\SelectImageFromApplication.tsx", ["1680", "1681", "1682", "1683", "1684", "1685", "1686", "1687"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\SpeechRecognitionService.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\AIService.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\DraggableCheckpoint.tsx", ["1688", "1689", "1690", "1691", "1692", "1693", "1694", "1695", "1696", "1697", "1698", "1699", "1700", "1701", "1702", "1703", "1704", "1705", "1706", "1707", "1708", "1709", "1710", "1711", "1712", "1713", "1714", "1715", "1716"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointEditPopup.tsx", ["1717", "1718", "1719", "1720", "1721", "1722", "1723", "1724", "1725", "1726", "1727", "1728", "1729", "1730", "1731", "1732", "1733", "1734", "1735", "1736", "1737", "1738", "1739", "1740", "1741", "1742", "1743", "1744", "1745", "1746", "1747", "1748", "1749", "1750", "1751", "1752", "1753", "1754", "1755", "1756", "1757", "1758", "1759", "1760", "1761", "1762", "1763", "1764", "1765"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointAddPopup.tsx", ["1766", "1767", "1768", "1769", "1770", "1771", "1772", "1773", "1774", "1775", "1776", "1777", "1778", "1779", "1780", "1781", "1782", "1783", "1784", "1785", "1786", "1787", "1788", "1789", "1790", "1791", "1792", "1793", "1794", "1795", "1796", "1797", "1798", "1799", "1800", "1801", "1802", "1803", "1804", "1805", "1806", "1807", "1808", "1809", "1810", "1811", "1812", "1813", "1814", "1815", "1816", "1817", "1818", "1819", "1820", "1821", "1822", "1823", "1824", "1825", "1826", "1827"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageProperties.tsx", ["1828", "1829", "1830", "1831", "1832", "1833", "1834", "1835", "1836", "1837", "1838", "1839", "1840", "1841"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageGalleryPopup.tsx", ["1842", "1843"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\Buttons.tsx", ["1844", "1845", "1846", "1847", "1848", "1849", "1850", "1851", "1852", "1853", "1854"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ImageSection.tsx", ["1855", "1856", "1857", "1858", "1859", "1860", "1861", "1862", "1863", "1864", "1865", "1866", "1867"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\RTE\\RTESection.tsx", ["1868", "1869", "1870", "1871", "1872", "1873", "1874", "1875", "1876", "1877", "1878", "1879", "1880", "1881", "1882", "1883", "1884", "1885", "1886", "1887", "1888", "1889", "1890", "1891", "1892", "1893", "1894", "1895", "1896", "1897", "1898", "1899"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ButtonSetting.tsx", ["1900", "1901", "1902", "1903", "1904", "1905", "1906", "1907", "1908", "1909", "1910", "1911", "1912"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\UserRoleService.ts", ["1913"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\i18n.ts", ["1914", "1915", "1916"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\contexts\\TranslationContext.tsx", ["1917", "1918"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\LanguageSelector.tsx", ["1919"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\LanguageService.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\AgentTraining.tsx", ["1920", "1921", "1922", "1923", "1924", "1925", "1926", "1927", "1928", "1929", "1930", "1931", "1932", "1933", "1934", "1935", "1936", "1937", "1938", "1939"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\AgentAdditionalContextPopup.tsx", [], [], {"ruleId": "1940", "severity": 1, "message": "1941", "line": 3, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 3, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "1944", "line": 9, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 11}, {"ruleId": "1940", "severity": 1, "message": "1945", "line": 16, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 16, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "1946", "line": 1, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "1947", "line": 1, "column": 58, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 65}, {"ruleId": "1940", "severity": 1, "message": "1948", "line": 5, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 5, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "1949", "line": 6, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 6, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "1950", "line": 7, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 7, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "1951", "line": 14, "column": 28, "nodeType": "1942", "messageId": "1943", "endLine": 14, "endColumn": 40}, {"ruleId": "1940", "severity": 1, "message": "1952", "line": 19, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 19, "endColumn": 9}, {"ruleId": "1940", "severity": 1, "message": "1953", "line": 24, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 24, "endColumn": 10}, {"ruleId": "1940", "severity": 1, "message": "1954", "line": 25, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 25, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "1955", "line": 26, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 26, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "1956", "line": 27, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 27, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "1957", "line": 28, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 28, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "1958", "line": 29, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 29, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "1959", "line": 30, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 30, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "1960", "line": 31, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 31, "endColumn": 12}, {"ruleId": "1940", "severity": 1, "message": "1961", "line": 32, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 32, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "1962", "line": 33, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 33, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "1963", "line": 34, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 34, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "1964", "line": 35, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 35, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "1965", "line": 36, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 36, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "1966", "line": 37, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 37, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "1967", "line": 39, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 39, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "1968", "line": 40, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 40, "endColumn": 10}, {"ruleId": "1940", "severity": 1, "message": "1969", "line": 41, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 41, "endColumn": 10}, {"ruleId": "1940", "severity": 1, "message": "1970", "line": 42, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 42, "endColumn": 11}, {"ruleId": "1940", "severity": 1, "message": "1971", "line": 46, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 46, "endColumn": 10}, {"ruleId": "1940", "severity": 1, "message": "1972", "line": 52, "column": 20, "nodeType": "1942", "messageId": "1943", "endLine": 52, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "1973", "line": 62, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 62, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "1974", "line": 63, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 63, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "1975", "line": 70, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 70, "endColumn": 6}, {"ruleId": "1940", "severity": 1, "message": "1976", "line": 71, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 71, "endColumn": 6}, {"ruleId": "1940", "severity": 1, "message": "1977", "line": 78, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 78, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "1978", "line": 80, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 80, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "1979", "line": 81, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 81, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "1980", "line": 81, "column": 30, "nodeType": "1942", "messageId": "1943", "endLine": 81, "endColumn": 39}, {"ruleId": "1940", "severity": 1, "message": "1981", "line": 84, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 84, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "1982", "line": 85, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 85, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "1983", "line": 86, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 86, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "1984", "line": 90, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 90, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "1985", "line": 92, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 92, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "1986", "line": 98, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 98, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "1987", "line": 105, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 105, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "1988", "line": 108, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 108, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "1989", "line": 109, "column": 26, "nodeType": "1942", "messageId": "1943", "endLine": 109, "endColumn": 37}, {"ruleId": "1940", "severity": 1, "message": "1990", "line": 114, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 114, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "1991", "line": 114, "column": 21, "nodeType": "1942", "messageId": "1943", "endLine": 114, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "1992", "line": 117, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 117, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "1993", "line": 124, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 124, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "1994", "line": 138, "column": 11, "nodeType": "1942", "messageId": "1943", "endLine": 138, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "1995", "line": 201, "column": 11, "nodeType": "1942", "messageId": "1943", "endLine": 201, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "1996", "line": 218, "column": 11, "nodeType": "1942", "messageId": "1943", "endLine": 218, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "1997", "line": 226, "column": 11, "nodeType": "1942", "messageId": "1943", "endLine": 226, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "1998", "line": 382, "column": 11, "nodeType": "1942", "messageId": "1943", "endLine": 382, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "1999", "line": 417, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 417, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2000", "line": 419, "column": 6, "nodeType": "1942", "messageId": "1943", "endLine": 419, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2001", "line": 422, "column": 6, "nodeType": "1942", "messageId": "1943", "endLine": 422, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2002", "line": 438, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 438, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2003", "line": 439, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 439, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2004", "line": 441, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 441, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "2005", "line": 444, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 444, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2006", "line": 448, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 448, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2007", "line": 449, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 449, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2008", "line": 460, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 460, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2009", "line": 461, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 461, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2010", "line": 462, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 462, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2011", "line": 464, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 464, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2012", "line": 464, "column": 26, "nodeType": "1942", "messageId": "1943", "endLine": 464, "endColumn": 44}, {"ruleId": "1940", "severity": 1, "message": "2013", "line": 469, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 469, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2014", "line": 469, "column": 23, "nodeType": "1942", "messageId": "1943", "endLine": 469, "endColumn": 38}, {"ruleId": "1940", "severity": 1, "message": "2015", "line": 471, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 471, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2016", "line": 471, "column": 19, "nodeType": "1942", "messageId": "1943", "endLine": 471, "endColumn": 30}, {"ruleId": "1940", "severity": 1, "message": "2017", "line": 474, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 474, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2018", "line": 474, "column": 24, "nodeType": "1942", "messageId": "1943", "endLine": 474, "endColumn": 40}, {"ruleId": "1940", "severity": 1, "message": "2019", "line": 475, "column": 19, "nodeType": "1942", "messageId": "1943", "endLine": 475, "endColumn": 30}, {"ruleId": "1940", "severity": 1, "message": "2020", "line": 480, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 480, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2021", "line": 480, "column": 29, "nodeType": "1942", "messageId": "1943", "endLine": 480, "endColumn": 50}, {"ruleId": "1940", "severity": 1, "message": "2022", "line": 487, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 487, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2023", "line": 487, "column": 16, "nodeType": "1942", "messageId": "1943", "endLine": 487, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2024", "line": 489, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 489, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2025", "line": 489, "column": 27, "nodeType": "1942", "messageId": "1943", "endLine": 489, "endColumn": 41}, {"ruleId": "1940", "severity": 1, "message": "2026", "line": 491, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 491, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2027", "line": 491, "column": 16, "nodeType": "1942", "messageId": "1943", "endLine": 491, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2028", "line": 505, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 505, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2029", "line": 506, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 506, "endColumn": 31}, {"ruleId": "1940", "severity": 1, "message": "2030", "line": 506, "column": 33, "nodeType": "1942", "messageId": "1943", "endLine": 506, "endColumn": 58}, {"ruleId": "1940", "severity": 1, "message": "2031", "line": 509, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 509, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2032", "line": 509, "column": 19, "nodeType": "1942", "messageId": "1943", "endLine": 509, "endColumn": 30}, {"ruleId": "1940", "severity": 1, "message": "2033", "line": 510, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 510, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2034", "line": 510, "column": 17, "nodeType": "1942", "messageId": "1943", "endLine": 510, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2035", "line": 511, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 511, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2036", "line": 511, "column": 21, "nodeType": "1942", "messageId": "1943", "endLine": 511, "endColumn": 34}, {"ruleId": "1940", "severity": 1, "message": "2037", "line": 520, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 520, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2038", "line": 521, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 521, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2039", "line": 527, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 527, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2040", "line": 531, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 531, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2041", "line": 531, "column": 26, "nodeType": "1942", "messageId": "1943", "endLine": 531, "endColumn": 44}, {"ruleId": "1940", "severity": 1, "message": "2042", "line": 534, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 534, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2043", "line": 534, "column": 20, "nodeType": "1942", "messageId": "1943", "endLine": 534, "endColumn": 32}, {"ruleId": "2044", "severity": 1, "message": "2045", "line": 574, "column": 5, "nodeType": "2046", "endLine": 574, "endColumn": 27, "suggestions": "2047"}, {"ruleId": "1940", "severity": 1, "message": "2048", "line": 584, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 584, "endColumn": 6}, {"ruleId": "1940", "severity": 1, "message": "2049", "line": 585, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 585, "endColumn": 7}, {"ruleId": "1940", "severity": 1, "message": "2050", "line": 586, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 586, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2051", "line": 588, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 588, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2052", "line": 589, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 589, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2053", "line": 594, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 594, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2054", "line": 595, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 595, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2055", "line": 630, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 630, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2056", "line": 631, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 631, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2057", "line": 632, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 632, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2058", "line": 640, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 640, "endColumn": 10}, {"ruleId": "1940", "severity": 1, "message": "2059", "line": 642, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 642, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2060", "line": 643, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 643, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2061", "line": 644, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 644, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2062", "line": 645, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 645, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2063", "line": 650, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 650, "endColumn": 11}, {"ruleId": "1940", "severity": 1, "message": "2064", "line": 652, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 652, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2065", "line": 654, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 654, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2066", "line": 664, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 664, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2067", "line": 665, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 665, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2068", "line": 668, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 668, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2069", "line": 672, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 672, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2070", "line": 674, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 674, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "2071", "line": 675, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 675, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2072", "line": 677, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 677, "endColumn": 11}, {"ruleId": "1940", "severity": 1, "message": "2073", "line": 684, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 684, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2074", "line": 685, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 685, "endColumn": 12}, {"ruleId": "1940", "severity": 1, "message": "2075", "line": 690, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 690, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2076", "line": 691, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 691, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2077", "line": 692, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 692, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2078", "line": 702, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 702, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2079", "line": 706, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 706, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2080", "line": 710, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 710, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2081", "line": 712, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 712, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2082", "line": 714, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 714, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2083", "line": 715, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 715, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2084", "line": 720, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 720, "endColumn": 12}, {"ruleId": "1940", "severity": 1, "message": "2085", "line": 721, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 721, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2086", "line": 740, "column": 18, "nodeType": "1942", "messageId": "1943", "endLine": 740, "endColumn": 37}, {"ruleId": "1940", "severity": 1, "message": "2087", "line": 741, "column": 18, "nodeType": "1942", "messageId": "1943", "endLine": 741, "endColumn": 37}, {"ruleId": "1940", "severity": 1, "message": "2088", "line": 745, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 745, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2089", "line": 757, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 757, "endColumn": 35}, {"ruleId": "1940", "severity": 1, "message": "2090", "line": 790, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 790, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2091", "line": 801, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 801, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2092", "line": 806, "column": 25, "nodeType": "1942", "messageId": "1943", "endLine": 806, "endColumn": 42}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 811, "column": 22, "nodeType": "2095", "messageId": "2096", "endLine": 811, "endColumn": 24}, {"ruleId": "2044", "severity": 1, "message": "2097", "line": 897, "column": 5, "nodeType": "2046", "endLine": 897, "endColumn": 46, "suggestions": "2098"}, {"ruleId": "2044", "severity": 1, "message": "2099", "line": 897, "column": 6, "nodeType": "2100", "endLine": 897, "endColumn": 29}, {"ruleId": "2044", "severity": 1, "message": "2101", "line": 915, "column": 5, "nodeType": "2046", "endLine": 915, "endColumn": 18, "suggestions": "2102"}, {"ruleId": "1940", "severity": 1, "message": "2103", "line": 917, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 917, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2104", "line": 918, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 918, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2105", "line": 939, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 939, "endColumn": 24}, {"ruleId": "2044", "severity": 1, "message": "2106", "line": 1000, "column": 5, "nodeType": "2046", "endLine": 1008, "endColumn": 3, "suggestions": "2107"}, {"ruleId": "2044", "severity": 1, "message": "2108", "line": 1036, "column": 5, "nodeType": "2046", "endLine": 1059, "endColumn": 3, "suggestions": "2109"}, {"ruleId": "2044", "severity": 1, "message": "2110", "line": 1071, "column": 5, "nodeType": "2046", "endLine": 1071, "endColumn": 19, "suggestions": "2111"}, {"ruleId": "2044", "severity": 1, "message": "2112", "line": 1182, "column": 5, "nodeType": "2046", "endLine": 1182, "endColumn": 39, "suggestions": "2113"}, {"ruleId": "1940", "severity": 1, "message": "2114", "line": 1301, "column": 16, "nodeType": "1942", "messageId": "1943", "endLine": 1301, "endColumn": 24}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 1393, "column": 25, "nodeType": "2095", "messageId": "2096", "endLine": 1393, "endColumn": 27}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 1400, "column": 25, "nodeType": "2095", "messageId": "2096", "endLine": 1400, "endColumn": 27}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 1400, "column": 53, "nodeType": "2095", "messageId": "2096", "endLine": 1400, "endColumn": 55}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 1403, "column": 26, "nodeType": "2095", "messageId": "2096", "endLine": 1403, "endColumn": 28}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 1403, "column": 58, "nodeType": "2095", "messageId": "2096", "endLine": 1403, "endColumn": 60}, {"ruleId": "1940", "severity": 1, "message": "2116", "line": 1534, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 1534, "endColumn": 33}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 1611, "column": 19, "nodeType": "2095", "messageId": "2096", "endLine": 1611, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2117", "line": 1758, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 1758, "endColumn": 30}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 2020, "column": 19, "nodeType": "2095", "messageId": "2096", "endLine": 2020, "endColumn": 21}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 2192, "column": 25, "nodeType": "2095", "messageId": "2096", "endLine": 2192, "endColumn": 27}, {"ruleId": "2044", "severity": 1, "message": "2118", "line": 2231, "column": 5, "nodeType": "2046", "endLine": 2231, "endColumn": 69, "suggestions": "2119"}, {"ruleId": "1940", "severity": 1, "message": "2120", "line": 2288, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 2288, "endColumn": 36}, {"ruleId": "1940", "severity": 1, "message": "2121", "line": 2299, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 2299, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2122", "line": 2299, "column": 29, "nodeType": "1942", "messageId": "1943", "endLine": 2299, "endColumn": 48}, {"ruleId": "1940", "severity": 1, "message": "2123", "line": 2691, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 2691, "endColumn": 27}, {"ruleId": "2044", "severity": 1, "message": "2124", "line": 2726, "column": 5, "nodeType": "2046", "endLine": 2726, "endColumn": 38, "suggestions": "2125"}, {"ruleId": "1940", "severity": 1, "message": "2126", "line": 2743, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 2743, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "2127", "line": 2777, "column": 6, "nodeType": "1942", "messageId": "1943", "endLine": 2777, "endColumn": 18}, {"ruleId": "2044", "severity": 1, "message": "2128", "line": 3177, "column": 4, "nodeType": "2046", "endLine": 3177, "endColumn": 18, "suggestions": "2129"}, {"ruleId": "1940", "severity": 1, "message": "2130", "line": 3539, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 3539, "endColumn": 33}, {"ruleId": "2044", "severity": 1, "message": "2131", "line": 3613, "column": 16, "nodeType": "2100", "endLine": 3613, "endColumn": 37}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 3614, "column": 56, "nodeType": "2095", "messageId": "2096", "endLine": 3614, "endColumn": 58}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 3618, "column": 49, "nodeType": "2095", "messageId": "2096", "endLine": 3618, "endColumn": 51}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 3622, "column": 50, "nodeType": "2095", "messageId": "2096", "endLine": 3622, "endColumn": 52}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 3628, "column": 51, "nodeType": "2095", "messageId": "2096", "endLine": 3628, "endColumn": 53}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 3635, "column": 51, "nodeType": "2095", "messageId": "2096", "endLine": 3635, "endColumn": 53}, {"ruleId": "1940", "severity": 1, "message": "2132", "line": 3858, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 3858, "endColumn": 23}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 3866, "column": 30, "nodeType": "2095", "messageId": "2096", "endLine": 3866, "endColumn": 32}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 3879, "column": 39, "nodeType": "2095", "messageId": "2096", "endLine": 3879, "endColumn": 41}, {"ruleId": "2044", "severity": 1, "message": "2133", "line": 3893, "column": 5, "nodeType": "2046", "endLine": 3893, "endColumn": 33, "suggestions": "2134"}, {"ruleId": "1940", "severity": 1, "message": "2135", "line": 3897, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 3897, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "2136", "line": 3897, "column": 30, "nodeType": "1942", "messageId": "1943", "endLine": 3897, "endColumn": 52}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 3995, "column": 55, "nodeType": "2095", "messageId": "2096", "endLine": 3995, "endColumn": 57}, {"ruleId": "1940", "severity": 1, "message": "2137", "line": 4014, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 4014, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2138", "line": 4016, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 4016, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2139", "line": 4020, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 4020, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2140", "line": 4039, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 4039, "endColumn": 26}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 4073, "column": 66, "nodeType": "2095", "messageId": "2096", "endLine": 4073, "endColumn": 68}, {"ruleId": "2044", "severity": 1, "message": "2141", "line": 4080, "column": 5, "nodeType": "2046", "endLine": 4087, "endColumn": 3, "suggestions": "2142"}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 4321, "column": 17, "nodeType": "2095", "messageId": "2096", "endLine": 4321, "endColumn": 19}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 4576, "column": 21, "nodeType": "2095", "messageId": "2096", "endLine": 4576, "endColumn": 23}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 4584, "column": 21, "nodeType": "2095", "messageId": "2096", "endLine": 4584, "endColumn": 23}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 4597, "column": 15, "nodeType": "2095", "messageId": "2096", "endLine": 4597, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2143", "line": 4894, "column": 11, "nodeType": "1942", "messageId": "1943", "endLine": 4894, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2144", "line": 4905, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 4905, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2145", "line": 4906, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 4906, "endColumn": 20}, {"ruleId": "2044", "severity": 1, "message": "2146", "line": 4912, "column": 5, "nodeType": "2046", "endLine": 4912, "endColumn": 62, "suggestions": "2147"}, {"ruleId": "2044", "severity": 1, "message": "2099", "line": 4912, "column": 6, "nodeType": "2148", "endLine": 4912, "endColumn": 48}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 4935, "column": 25, "nodeType": "2095", "messageId": "2096", "endLine": 4935, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2149", "line": 4939, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 4939, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2150", "line": 4962, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 4962, "endColumn": 23}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 5037, "column": 25, "nodeType": "2095", "messageId": "2096", "endLine": 5037, "endColumn": 27}, {"ruleId": "2044", "severity": 1, "message": "2151", "line": 5070, "column": 5, "nodeType": "2046", "endLine": 5070, "endColumn": 22, "suggestions": "2152"}, {"ruleId": "1940", "severity": 1, "message": "2153", "line": 5072, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 5072, "endColumn": 18}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 5074, "column": 40, "nodeType": "2095", "messageId": "2096", "endLine": 5074, "endColumn": 42}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 5139, "column": 69, "nodeType": "2095", "messageId": "2096", "endLine": 5139, "endColumn": 71}, {"ruleId": "1940", "severity": 1, "message": "2154", "line": 5189, "column": 12, "nodeType": "1942", "messageId": "1943", "endLine": 5189, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2155", "line": 5190, "column": 12, "nodeType": "1942", "messageId": "1943", "endLine": 5190, "endColumn": 22}, {"ruleId": "2044", "severity": 1, "message": "2156", "line": 5220, "column": 5, "nodeType": "2046", "endLine": 5220, "endColumn": 38, "suggestions": "2157"}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 5223, "column": 40, "nodeType": "2095", "messageId": "2096", "endLine": 5223, "endColumn": 42}, {"ruleId": "1940", "severity": 1, "message": "2154", "line": 5229, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 5229, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2155", "line": 5230, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 5230, "endColumn": 20}, {"ruleId": "2044", "severity": 1, "message": "2158", "line": 5236, "column": 5, "nodeType": "2046", "endLine": 5236, "endColumn": 106, "suggestions": "2159"}, {"ruleId": "2044", "severity": 1, "message": "2160", "line": 5385, "column": 5, "nodeType": "2046", "endLine": 5385, "endColumn": 17, "suggestions": "2161"}, {"ruleId": "2044", "severity": 1, "message": "2162", "line": 5401, "column": 5, "nodeType": "2046", "endLine": 5401, "endColumn": 78, "suggestions": "2163"}, {"ruleId": "1940", "severity": 1, "message": "2164", "line": 5404, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 5404, "endColumn": 29}, {"ruleId": "2044", "severity": 1, "message": "2165", "line": 5417, "column": 8, "nodeType": "2046", "endLine": 5417, "endColumn": 15, "suggestions": "2166"}, {"ruleId": "2167", "severity": 1, "message": "2168", "line": 6076, "column": 80, "nodeType": "2169", "messageId": "2170", "endLine": 6076, "endColumn": 81, "suggestions": "2171"}, {"ruleId": "1940", "severity": 1, "message": "2172", "line": 6281, "column": 25, "nodeType": "1942", "messageId": "1943", "endLine": 6281, "endColumn": 42}, {"ruleId": "1940", "severity": 1, "message": "2173", "line": 2, "column": 16, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2174", "line": 7, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 7, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2175", "line": 7, "column": 23, "nodeType": "1942", "messageId": "1943", "endLine": 7, "endColumn": 34}, {"ruleId": "1940", "severity": 1, "message": "2176", "line": 98, "column": 13, "nodeType": "1942", "messageId": "1943", "endLine": 98, "endColumn": 25}, {"ruleId": "2044", "severity": 1, "message": "2177", "line": 103, "column": 6, "nodeType": "2046", "endLine": 103, "endColumn": 8, "suggestions": "2178"}, {"ruleId": "1940", "severity": 1, "message": "2179", "line": 148, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 148, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2180", "line": 3, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 3, "endColumn": 31}, {"ruleId": "1940", "severity": 1, "message": "2181", "line": 4, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 32}, {"ruleId": "1940", "severity": 1, "message": "2182", "line": 3, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 3, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2183", "line": 8, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 8, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2184", "line": 9, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2185", "line": 13, "column": 24, "nodeType": "1942", "messageId": "1943", "endLine": 13, "endColumn": 46}, {"ruleId": "2186", "severity": 1, "message": "2187", "line": 2494, "column": 5, "nodeType": "2188", "messageId": "2096", "endLine": 2494, "endColumn": 17}, {"ruleId": "2186", "severity": 1, "message": "2189", "line": 2495, "column": 5, "nodeType": "2188", "messageId": "2096", "endLine": 2495, "endColumn": 20}, {"ruleId": "2186", "severity": 1, "message": "2190", "line": 2826, "column": 5, "nodeType": "2188", "messageId": "2096", "endLine": 2826, "endColumn": 24}, {"ruleId": "2191", "severity": 1, "message": "2192", "line": 2832, "column": 31, "nodeType": "2100", "messageId": "2193", "endLine": 2832, "endColumn": 51}, {"ruleId": "2186", "severity": 1, "message": "2194", "line": 3003, "column": 5, "nodeType": "2188", "messageId": "2096", "endLine": 3003, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2195", "line": 3716, "column": 16, "nodeType": "1942", "messageId": "1943", "endLine": 3716, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "2195", "line": 3915, "column": 16, "nodeType": "1942", "messageId": "1943", "endLine": 3915, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "2196", "line": 5227, "column": 14, "nodeType": "1942", "messageId": "1943", "endLine": 5227, "endColumn": 35}, {"ruleId": "2186", "severity": 1, "message": "2197", "line": 5403, "column": 5, "nodeType": "2188", "messageId": "2096", "endLine": 5403, "endColumn": 30}, {"ruleId": "1940", "severity": 1, "message": "2198", "line": 5475, "column": 14, "nodeType": "1942", "messageId": "1943", "endLine": 5475, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2199", "line": 6566, "column": 13, "nodeType": "1942", "messageId": "1943", "endLine": 6566, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2199", "line": 6592, "column": 13, "nodeType": "1942", "messageId": "1943", "endLine": 6592, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2199", "line": 6598, "column": 13, "nodeType": "1942", "messageId": "1943", "endLine": 6598, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2199", "line": 6613, "column": 13, "nodeType": "1942", "messageId": "1943", "endLine": 6613, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2198", "line": 7390, "column": 13, "nodeType": "1942", "messageId": "1943", "endLine": 7390, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2198", "line": 7634, "column": 13, "nodeType": "1942", "messageId": "1943", "endLine": 7634, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2198", "line": 7805, "column": 13, "nodeType": "1942", "messageId": "1943", "endLine": 7805, "endColumn": 16}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 8470, "column": 66, "nodeType": "2095", "messageId": "2096", "endLine": 8470, "endColumn": 68}, {"ruleId": "1940", "severity": 1, "message": "2200", "line": 70, "column": 23, "nodeType": "1942", "messageId": "1943", "endLine": 70, "endColumn": 29}, {"ruleId": "1940", "severity": 1, "message": "2201", "line": 1, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2202", "line": 2, "column": 44, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 56}, {"ruleId": "1940", "severity": 1, "message": "2203", "line": 18, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 18, "endColumn": 12}, {"ruleId": "1940", "severity": 1, "message": "2204", "line": 19, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 19, "endColumn": 7}, {"ruleId": "1940", "severity": 1, "message": "2205", "line": 20, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 20, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2206", "line": 21, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 21, "endColumn": 7}, {"ruleId": "1940", "severity": 1, "message": "2207", "line": 24, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 24, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2208", "line": 25, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 25, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2209", "line": 26, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 26, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2210", "line": 31, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 31, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2211", "line": 38, "column": 49, "nodeType": "1942", "messageId": "1943", "endLine": 38, "endColumn": 55}, {"ruleId": "1940", "severity": 1, "message": "2212", "line": 38, "column": 63, "nodeType": "1942", "messageId": "1943", "endLine": 38, "endColumn": 70}, {"ruleId": "1940", "severity": 1, "message": "2213", "line": 46, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 46, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2214", "line": 48, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 48, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2215", "line": 92, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 92, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2216", "line": 93, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 93, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2217", "line": 99, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 99, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2218", "line": 100, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 100, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2219", "line": 104, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 104, "endColumn": 10}, {"ruleId": "1940", "severity": 1, "message": "2220", "line": 108, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 108, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2221", "line": 112, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 112, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2222", "line": 113, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 113, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2223", "line": 114, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 114, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "2224", "line": 115, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 115, "endColumn": 33}, {"ruleId": "1940", "severity": 1, "message": "2225", "line": 116, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 116, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2226", "line": 119, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 119, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2227", "line": 120, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 120, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2228", "line": 165, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 165, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2229", "line": 175, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 175, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2230", "line": 183, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 183, "endColumn": 11}, {"ruleId": "1940", "severity": 1, "message": "2231", "line": 184, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 184, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2232", "line": 185, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 185, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2233", "line": 186, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 186, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2234", "line": 187, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 187, "endColumn": 10}, {"ruleId": "1940", "severity": 1, "message": "2235", "line": 208, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 208, "endColumn": 30}, {"ruleId": "1940", "severity": 1, "message": "2236", "line": 212, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 212, "endColumn": 29}, {"ruleId": "1940", "severity": 1, "message": "2237", "line": 459, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 459, "endColumn": 26}, {"ruleId": "2044", "severity": 1, "message": "2238", "line": 551, "column": 5, "nodeType": "2046", "endLine": 551, "endColumn": 60, "suggestions": "2239"}, {"ruleId": "1940", "severity": 1, "message": "2240", "line": 565, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 565, "endColumn": 22}, {"ruleId": "2044", "severity": 1, "message": "2241", "line": 585, "column": 5, "nodeType": "2046", "endLine": 585, "endColumn": 60, "suggestions": "2242"}, {"ruleId": "2044", "severity": 1, "message": "2243", "line": 602, "column": 4, "nodeType": "2046", "endLine": 602, "endColumn": 6, "suggestions": "2244"}, {"ruleId": "1940", "severity": 1, "message": "2245", "line": 2, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2246", "line": 1, "column": 17, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2247", "line": 2, "column": 29, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 39}, {"ruleId": "1940", "severity": 1, "message": "1981", "line": 3, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 3, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "1982", "line": 4, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "1979", "line": 5, "column": 26, "nodeType": "1942", "messageId": "1943", "endLine": 5, "endColumn": 44}, {"ruleId": "1940", "severity": 1, "message": "1980", "line": 5, "column": 46, "nodeType": "1942", "messageId": "1943", "endLine": 5, "endColumn": 55}, {"ruleId": "1940", "severity": 1, "message": "2248", "line": 6, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 6, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2249", "line": 6, "column": 24, "nodeType": "1942", "messageId": "1943", "endLine": 6, "endColumn": 31}, {"ruleId": "1940", "severity": 1, "message": "1991", "line": 11, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 11, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2250", "line": 17, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 17, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2251", "line": 21, "column": 19, "nodeType": "1942", "messageId": "1943", "endLine": 21, "endColumn": 35}, {"ruleId": "1940", "severity": 1, "message": "2252", "line": 24, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 24, "endColumn": 42}, {"ruleId": "1940", "severity": 1, "message": "2253", "line": 25, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 25, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2254", "line": 26, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 26, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2024", "line": 35, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 35, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2025", "line": 35, "column": 28, "nodeType": "1942", "messageId": "1943", "endLine": 35, "endColumn": 42}, {"ruleId": "1940", "severity": 1, "message": "1956", "line": 6, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 6, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2255", "line": 9, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 7}, {"ruleId": "1940", "severity": 1, "message": "2256", "line": 12, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 12, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2257", "line": 25, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 25, "endColumn": 38}, {"ruleId": "1940", "severity": 1, "message": "2258", "line": 51, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 51, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2259", "line": 52, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 52, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2063", "line": 53, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 53, "endColumn": 11}, {"ruleId": "1940", "severity": 1, "message": "2260", "line": 54, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 54, "endColumn": 8}, {"ruleId": "1940", "severity": 1, "message": "2261", "line": 55, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 55, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2080", "line": 56, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 56, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2262", "line": 57, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 57, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2263", "line": 58, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 58, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2081", "line": 59, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 59, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2264", "line": 60, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 60, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2265", "line": 61, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 61, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2266", "line": 62, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 62, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2267", "line": 63, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 63, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2268", "line": 64, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 64, "endColumn": 29}, {"ruleId": "1940", "severity": 1, "message": "2222", "line": 65, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 65, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2225", "line": 66, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 66, "endColumn": 23}, {"ruleId": "2044", "severity": 1, "message": "2269", "line": 83, "column": 5, "nodeType": "2046", "endLine": 83, "endColumn": 7, "suggestions": "2270"}, {"ruleId": "2044", "severity": 1, "message": "2271", "line": 101, "column": 5, "nodeType": "2046", "endLine": 101, "endColumn": 28, "suggestions": "2272"}, {"ruleId": "2044", "severity": 1, "message": "2273", "line": 112, "column": 5, "nodeType": "2046", "endLine": 112, "endColumn": 48, "suggestions": "2274"}, {"ruleId": "1940", "severity": 1, "message": "2275", "line": 191, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 191, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2276", "line": 3, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 3, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "1989", "line": 3, "column": 26, "nodeType": "1942", "messageId": "1943", "endLine": 3, "endColumn": 37}, {"ruleId": "1940", "severity": 1, "message": "2277", "line": 5, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 5, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2278", "line": 457, "column": 7, "nodeType": "1942", "messageId": "1943", "endLine": 457, "endColumn": 29}, {"ruleId": "1940", "severity": 1, "message": "2279", "line": 530, "column": 7, "nodeType": "1942", "messageId": "1943", "endLine": 530, "endColumn": 24}, {"ruleId": "2167", "severity": 1, "message": "2168", "line": 682, "column": 41, "nodeType": "2169", "messageId": "2170", "endLine": 682, "endColumn": 42, "suggestions": "2280"}, {"ruleId": "2167", "severity": 1, "message": "2281", "line": 682, "column": 45, "nodeType": "2169", "messageId": "2170", "endLine": 682, "endColumn": 46, "suggestions": "2282"}, {"ruleId": "2167", "severity": 1, "message": "2168", "line": 682, "column": 56, "nodeType": "2169", "messageId": "2170", "endLine": 682, "endColumn": 57, "suggestions": "2283"}, {"ruleId": "2167", "severity": 1, "message": "2281", "line": 682, "column": 60, "nodeType": "2169", "messageId": "2170", "endLine": 682, "endColumn": 61, "suggestions": "2284"}, {"ruleId": "2167", "severity": 1, "message": "2168", "line": 682, "column": 89, "nodeType": "2169", "messageId": "2170", "endLine": 682, "endColumn": 90, "suggestions": "2285"}, {"ruleId": "2167", "severity": 1, "message": "2281", "line": 682, "column": 93, "nodeType": "2169", "messageId": "2170", "endLine": 682, "endColumn": 94, "suggestions": "2286"}, {"ruleId": "2167", "severity": 1, "message": "2168", "line": 682, "column": 104, "nodeType": "2169", "messageId": "2170", "endLine": 682, "endColumn": 105, "suggestions": "2287"}, {"ruleId": "2167", "severity": 1, "message": "2281", "line": 682, "column": 108, "nodeType": "2169", "messageId": "2170", "endLine": 682, "endColumn": 109, "suggestions": "2288"}, {"ruleId": "1940", "severity": 1, "message": "2289", "line": 1316, "column": 11, "nodeType": "1942", "messageId": "1943", "endLine": 1316, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2290", "line": 1321, "column": 11, "nodeType": "1942", "messageId": "1943", "endLine": 1321, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2291", "line": 1, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2292", "line": 3, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 3, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2276", "line": 4, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "1947", "line": 2, "column": 28, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 35}, {"ruleId": "1940", "severity": 1, "message": "2293", "line": 4, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2294", "line": 8, "column": 33, "nodeType": "1942", "messageId": "1943", "endLine": 8, "endColumn": 42}, {"ruleId": "1940", "severity": 1, "message": "2295", "line": 8, "column": 44, "nodeType": "1942", "messageId": "1943", "endLine": 8, "endColumn": 59}, {"ruleId": "1940", "severity": 1, "message": "2296", "line": 10, "column": 34, "nodeType": "1942", "messageId": "1943", "endLine": 10, "endColumn": 57}, {"ruleId": "1940", "severity": 1, "message": "2297", "line": 10, "column": 59, "nodeType": "1942", "messageId": "1943", "endLine": 10, "endColumn": 79}, {"ruleId": "1940", "severity": 1, "message": "2298", "line": 59, "column": 16, "nodeType": "1942", "messageId": "1943", "endLine": 59, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2299", "line": 124, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 124, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "1947", "line": 1, "column": 28, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 35}, {"ruleId": "1940", "severity": 1, "message": "2300", "line": 8, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 8, "endColumn": 33}, {"ruleId": "1940", "severity": 1, "message": "2294", "line": 9, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2301", "line": 80, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 80, "endColumn": 58}, {"ruleId": "2044", "severity": 1, "message": "2302", "line": 86, "column": 8, "nodeType": "2303", "endLine": 90, "endColumn": 12}, {"ruleId": "2044", "severity": 1, "message": "2304", "line": 86, "column": 8, "nodeType": "2303", "endLine": 90, "endColumn": 12}, {"ruleId": "1940", "severity": 1, "message": "2305", "line": 92, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 92, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2306", "line": 92, "column": 25, "nodeType": "1942", "messageId": "1943", "endLine": 92, "endColumn": 42}, {"ruleId": "2307", "severity": 1, "message": "2308", "line": 113, "column": 113, "nodeType": "2309", "messageId": "2310", "endLine": 113, "endColumn": 397}, {"ruleId": "2044", "severity": 1, "message": "2311", "line": 154, "column": 5, "nodeType": "2046", "endLine": 154, "endColumn": 38, "suggestions": "2312"}, {"ruleId": "2044", "severity": 1, "message": "2099", "line": 154, "column": 6, "nodeType": "2095", "endLine": 154, "endColumn": 37}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 154, "column": 33, "nodeType": "2095", "messageId": "2096", "endLine": 154, "endColumn": 35}, {"ruleId": "1940", "severity": 1, "message": "2298", "line": 156, "column": 16, "nodeType": "1942", "messageId": "1943", "endLine": 156, "endColumn": 24}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 174, "column": 56, "nodeType": "2095", "messageId": "2096", "endLine": 174, "endColumn": 58}, {"ruleId": "1940", "severity": 1, "message": "2313", "line": 181, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 181, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2314", "line": 182, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 182, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2315", "line": 305, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 305, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2298", "line": 771, "column": 16, "nodeType": "1942", "messageId": "1943", "endLine": 771, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2316", "line": 806, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 806, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2317", "line": 806, "column": 19, "nodeType": "1942", "messageId": "1943", "endLine": 806, "endColumn": 30}, {"ruleId": "1940", "severity": 1, "message": "2318", "line": 807, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 807, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2319", "line": 807, "column": 22, "nodeType": "1942", "messageId": "1943", "endLine": 807, "endColumn": 36}, {"ruleId": "1940", "severity": 1, "message": "2121", "line": 808, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 808, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2122", "line": 808, "column": 29, "nodeType": "1942", "messageId": "1943", "endLine": 808, "endColumn": 48}, {"ruleId": "1940", "severity": 1, "message": "2320", "line": 809, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 809, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2321", "line": 809, "column": 27, "nodeType": "1942", "messageId": "1943", "endLine": 809, "endColumn": 46}, {"ruleId": "1940", "severity": 1, "message": "2038", "line": 810, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 810, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2322", "line": 3, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 3, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2323", "line": 4, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2324", "line": 31, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 31, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2325", "line": 32, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 32, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2246", "line": 1, "column": 17, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "1951", "line": 2, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2250", "line": 4, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "1973", "line": 9, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2326", "line": 11, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 11, "endColumn": 9}, {"ruleId": "1940", "severity": 1, "message": "2245", "line": 12, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 12, "endColumn": 8}, {"ruleId": "1940", "severity": 1, "message": "2327", "line": 14, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 14, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2209", "line": 16, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 16, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2328", "line": 18, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 18, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2329", "line": 19, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 19, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2330", "line": 20, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 20, "endColumn": 11}, {"ruleId": "1940", "severity": 1, "message": "2331", "line": 21, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 21, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2332", "line": 22, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 22, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2333", "line": 23, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 23, "endColumn": 12}, {"ruleId": "1940", "severity": 1, "message": "2334", "line": 24, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 24, "endColumn": 10}, {"ruleId": "1940", "severity": 1, "message": "2335", "line": 25, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 25, "endColumn": 9}, {"ruleId": "1940", "severity": 1, "message": "1976", "line": 3, "column": 65, "nodeType": "1942", "messageId": "1943", "endLine": 3, "endColumn": 69}, {"ruleId": "1940", "severity": 1, "message": "2336", "line": 6, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 6, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2337", "line": 7, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 7, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2338", "line": 20, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 20, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2317", "line": 84, "column": 19, "nodeType": "1942", "messageId": "1943", "endLine": 84, "endColumn": 30}, {"ruleId": "1940", "severity": 1, "message": "2339", "line": 85, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 85, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2340", "line": 85, "column": 26, "nodeType": "1942", "messageId": "1943", "endLine": 85, "endColumn": 44}, {"ruleId": "1940", "severity": 1, "message": "2341", "line": 86, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 86, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2342", "line": 86, "column": 26, "nodeType": "1942", "messageId": "1943", "endLine": 86, "endColumn": 44}, {"ruleId": "1940", "severity": 1, "message": "2343", "line": 90, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 90, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2344", "line": 90, "column": 45, "nodeType": "1942", "messageId": "1943", "endLine": 90, "endColumn": 62}, {"ruleId": "1940", "severity": 1, "message": "2345", "line": 93, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 93, "endColumn": 10}, {"ruleId": "1940", "severity": 1, "message": "2346", "line": 94, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 94, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2031", "line": 95, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 95, "endColumn": 11}, {"ruleId": "1940", "severity": 1, "message": "2032", "line": 96, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 96, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2033", "line": 97, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 97, "endColumn": 9}, {"ruleId": "1940", "severity": 1, "message": "2034", "line": 98, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 98, "endColumn": 12}, {"ruleId": "1940", "severity": 1, "message": "2035", "line": 99, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 99, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2036", "line": 100, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 100, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2347", "line": 101, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 101, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2348", "line": 102, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 102, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2349", "line": 103, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 103, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2050", "line": 104, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 104, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2350", "line": 105, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 105, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2351", "line": 107, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 107, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2352", "line": 108, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 108, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2353", "line": 115, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 115, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "2354", "line": 116, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 116, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2355", "line": 118, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 118, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2356", "line": 119, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 119, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2357", "line": 120, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 120, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2358", "line": 121, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 121, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2359", "line": 122, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 122, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2360", "line": 123, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 123, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2361", "line": 132, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 132, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2362", "line": 137, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 137, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2216", "line": 139, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 139, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2223", "line": 140, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 140, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "2363", "line": 141, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 141, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2364", "line": 144, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 144, "endColumn": 11}, {"ruleId": "1940", "severity": 1, "message": "2221", "line": 145, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 145, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2365", "line": 146, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 146, "endColumn": 20}, {"ruleId": "2044", "severity": 1, "message": "2366", "line": 170, "column": 5, "nodeType": "2046", "endLine": 170, "endColumn": 45, "suggestions": "2367"}, {"ruleId": "1940", "severity": 1, "message": "2368", "line": 221, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 221, "endColumn": 29}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 241, "column": 24, "nodeType": "2095", "messageId": "2096", "endLine": 241, "endColumn": 26}, {"ruleId": "2044", "severity": 1, "message": "2369", "line": 315, "column": 7, "nodeType": "2046", "endLine": 315, "endColumn": 42, "suggestions": "2370"}, {"ruleId": "1940", "severity": 1, "message": "2371", "line": 339, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 339, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2372", "line": 340, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 340, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2373", "line": 488, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 488, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2374", "line": 491, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 491, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2375", "line": 500, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 500, "endColumn": 31}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 811, "column": 26, "nodeType": "2095", "messageId": "2096", "endLine": 811, "endColumn": 28}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 847, "column": 26, "nodeType": "2095", "messageId": "2096", "endLine": 847, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "2376", "line": 1032, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 1032, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2377", "line": 1036, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 1036, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2378", "line": 1040, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 1040, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "2379", "line": 1044, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 1044, "endColumn": 32}, {"ruleId": "1940", "severity": 1, "message": "2380", "line": 1048, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 1048, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2381", "line": 1052, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 1052, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2382", "line": 1056, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 1056, "endColumn": 31}, {"ruleId": "1940", "severity": 1, "message": "2383", "line": 1060, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 1060, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "2384", "line": 5, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 5, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2385", "line": 13, "column": 26, "nodeType": "1942", "messageId": "1943", "endLine": 13, "endColumn": 47}, {"ruleId": "1940", "severity": 1, "message": "2386", "line": 15, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 15, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2387", "line": 79, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 79, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2388", "line": 81, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 81, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2389", "line": 82, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 82, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2390", "line": 83, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 83, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2391", "line": 84, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 84, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2058", "line": 88, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 88, "endColumn": 10}, {"ruleId": "1940", "severity": 1, "message": "2392", "line": 89, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 89, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2393", "line": 91, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 91, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2060", "line": 92, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 92, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2061", "line": 93, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 93, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2059", "line": 94, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 94, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2226", "line": 104, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 104, "endColumn": 19}, {"ruleId": "2044", "severity": 1, "message": "2394", "line": 209, "column": 7, "nodeType": "2046", "endLine": 209, "endColumn": 9, "suggestions": "2395"}, {"ruleId": "2044", "severity": 1, "message": "2396", "line": 244, "column": 7, "nodeType": "2046", "endLine": 244, "endColumn": 29, "suggestions": "2397"}, {"ruleId": "2044", "severity": 1, "message": "2398", "line": 249, "column": 7, "nodeType": "2046", "endLine": 249, "endColumn": 18, "suggestions": "2399"}, {"ruleId": "2044", "severity": 1, "message": "2400", "line": 292, "column": 7, "nodeType": "2046", "endLine": 292, "endColumn": 72, "suggestions": "2401"}, {"ruleId": "1940", "severity": 1, "message": "2350", "line": 331, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 331, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2402", "line": 334, "column": 26, "nodeType": "1942", "messageId": "1943", "endLine": 334, "endColumn": 44}, {"ruleId": "1940", "severity": 1, "message": "2403", "line": 463, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 463, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2404", "line": 4, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2405", "line": 6, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 6, "endColumn": 35}, {"ruleId": "1940", "severity": 1, "message": "2406", "line": 7, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 7, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2407", "line": 7, "column": 17, "nodeType": "1942", "messageId": "1943", "endLine": 7, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2408", "line": 8, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 8, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2409", "line": 9, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2060", "line": 13, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 13, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2061", "line": 14, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 14, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2059", "line": 15, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 15, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2410", "line": 17, "column": 21, "nodeType": "1942", "messageId": "1943", "endLine": 17, "endColumn": 34}, {"ruleId": "1940", "severity": 1, "message": "2411", "line": 18, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 18, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2412", "line": 18, "column": 33, "nodeType": "1942", "messageId": "1943", "endLine": 18, "endColumn": 44}, {"ruleId": "1940", "severity": 1, "message": "2413", "line": 19, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 19, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2414", "line": 96, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 96, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2415", "line": 97, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 97, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2416", "line": 100, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 100, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2417", "line": 101, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 101, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2418", "line": 109, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 109, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2419", "line": 129, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 129, "endColumn": 16}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 176, "column": 45, "nodeType": "2095", "messageId": "2096", "endLine": 176, "endColumn": 47}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 176, "column": 104, "nodeType": "2095", "messageId": "2096", "endLine": 176, "endColumn": 106}, {"ruleId": "1940", "severity": 1, "message": "2210", "line": 2, "column": 60, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 73}, {"ruleId": "1940", "severity": 1, "message": "2404", "line": 3, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 3, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2420", "line": 8, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 8, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2421", "line": 9, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2422", "line": 133, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 133, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2423", "line": 134, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 134, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2424", "line": 135, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 135, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "2425", "line": 135, "column": 30, "nodeType": "1942", "messageId": "1943", "endLine": 135, "endColumn": 52}, {"ruleId": "1940", "severity": 1, "message": "2226", "line": 137, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 137, "endColumn": 19}, {"ruleId": "2044", "severity": 1, "message": "2426", "line": 163, "column": 8, "nodeType": "2046", "endLine": 163, "endColumn": 10, "suggestions": "2427"}, {"ruleId": "1940", "severity": 1, "message": "2428", "line": 299, "column": 11, "nodeType": "1942", "messageId": "1943", "endLine": 299, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2429", "line": 342, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 342, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2430", "line": 343, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 343, "endColumn": 35}, {"ruleId": "1940", "severity": 1, "message": "2431", "line": 344, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 344, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2432", "line": 346, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 346, "endColumn": 20}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 465, "column": 22, "nodeType": "2095", "messageId": "2096", "endLine": 465, "endColumn": 24}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 465, "column": 53, "nodeType": "2095", "messageId": "2096", "endLine": 465, "endColumn": 55}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 465, "column": 89, "nodeType": "2095", "messageId": "2096", "endLine": 465, "endColumn": 91}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 465, "column": 125, "nodeType": "2095", "messageId": "2096", "endLine": 465, "endColumn": 127}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 467, "column": 29, "nodeType": "2095", "messageId": "2096", "endLine": 467, "endColumn": 31}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 467, "column": 56, "nodeType": "2095", "messageId": "2096", "endLine": 467, "endColumn": 58}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 467, "column": 88, "nodeType": "2095", "messageId": "2096", "endLine": 467, "endColumn": 90}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 467, "column": 120, "nodeType": "2095", "messageId": "2096", "endLine": 467, "endColumn": 122}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 469, "column": 29, "nodeType": "2095", "messageId": "2096", "endLine": 469, "endColumn": 31}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 469, "column": 64, "nodeType": "2095", "messageId": "2096", "endLine": 469, "endColumn": 66}, {"ruleId": "1940", "severity": 1, "message": "2433", "line": 111, "column": 11, "nodeType": "1942", "messageId": "1943", "endLine": 111, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "2360", "line": 152, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 152, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2072", "line": 153, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 153, "endColumn": 11}, {"ruleId": "1940", "severity": 1, "message": "2434", "line": 159, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 159, "endColumn": 23}, {"ruleId": "2435", "severity": 1, "message": "2436", "line": 225, "column": 25, "nodeType": "1942", "messageId": "2437", "endLine": 225, "endColumn": 34, "suggestions": "2438"}, {"ruleId": "2044", "severity": 1, "message": "2439", "line": 231, "column": 5, "nodeType": "2046", "endLine": 231, "endColumn": 12, "suggestions": "2440"}, {"ruleId": "2044", "severity": 1, "message": "2441", "line": 237, "column": 5, "nodeType": "2046", "endLine": 237, "endColumn": 21, "suggestions": "2442"}, {"ruleId": "2044", "severity": 1, "message": "2443", "line": 472, "column": 5, "nodeType": "2046", "endLine": 472, "endColumn": 70, "suggestions": "2444"}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 547, "column": 19, "nodeType": "2095", "messageId": "2096", "endLine": 547, "endColumn": 21}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 548, "column": 19, "nodeType": "2095", "messageId": "2096", "endLine": 548, "endColumn": 21}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 549, "column": 24, "nodeType": "2095", "messageId": "2096", "endLine": 549, "endColumn": 26}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 550, "column": 24, "nodeType": "2095", "messageId": "2096", "endLine": 550, "endColumn": 26}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 554, "column": 19, "nodeType": "2095", "messageId": "2096", "endLine": 554, "endColumn": 21}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 555, "column": 19, "nodeType": "2095", "messageId": "2096", "endLine": 555, "endColumn": 21}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 556, "column": 24, "nodeType": "2095", "messageId": "2096", "endLine": 556, "endColumn": 26}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 557, "column": 24, "nodeType": "2095", "messageId": "2096", "endLine": 557, "endColumn": 26}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 561, "column": 19, "nodeType": "2095", "messageId": "2096", "endLine": 561, "endColumn": 21}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 562, "column": 24, "nodeType": "2095", "messageId": "2096", "endLine": 562, "endColumn": 26}, {"ruleId": "2044", "severity": 1, "message": "2445", "line": 582, "column": 5, "nodeType": "2046", "endLine": 582, "endColumn": 64, "suggestions": "2446"}, {"ruleId": "2044", "severity": 1, "message": "2099", "line": 582, "column": 6, "nodeType": "2148", "endLine": 582, "endColumn": 34}, {"ruleId": "1940", "severity": 1, "message": "2447", "line": 591, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 591, "endColumn": 21}, {"ruleId": "2044", "severity": 1, "message": "2448", "line": 605, "column": 5, "nodeType": "2046", "endLine": 605, "endColumn": 47, "suggestions": "2449"}, {"ruleId": "1940", "severity": 1, "message": "2447", "line": 614, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 614, "endColumn": 21}, {"ruleId": "2044", "severity": 1, "message": "2448", "line": 627, "column": 5, "nodeType": "2046", "endLine": 627, "endColumn": 47, "suggestions": "2450"}, {"ruleId": "2044", "severity": 1, "message": "2451", "line": 1021, "column": 17, "nodeType": "1942", "endLine": 1021, "endColumn": 32}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 1227, "column": 43, "nodeType": "2095", "messageId": "2096", "endLine": 1227, "endColumn": 45}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 1232, "column": 78, "nodeType": "2095", "messageId": "2096", "endLine": 1232, "endColumn": 80}, {"ruleId": "1940", "severity": 1, "message": "2452", "line": 1, "column": 17, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2453", "line": 2, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2454", "line": 2, "column": 16, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2182", "line": 3, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 3, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2455", "line": 11, "column": 62, "nodeType": "1942", "messageId": "1943", "endLine": 11, "endColumn": 67}, {"ruleId": "1940", "severity": 1, "message": "2267", "line": 25, "column": 7, "nodeType": "1942", "messageId": "1943", "endLine": 25, "endColumn": 29}, {"ruleId": "1940", "severity": 1, "message": "2225", "line": 28, "column": 7, "nodeType": "1942", "messageId": "1943", "endLine": 28, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2456", "line": 31, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 31, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2457", "line": 144, "column": 11, "nodeType": "1942", "messageId": "1943", "endLine": 144, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2458", "line": 145, "column": 11, "nodeType": "1942", "messageId": "1943", "endLine": 145, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2459", "line": 1, "column": 28, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 34}, {"ruleId": "1940", "severity": 1, "message": "2245", "line": 5, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 5, "endColumn": 5}, {"ruleId": "1940", "severity": 1, "message": "2460", "line": 6, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 6, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2461", "line": 10, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 10, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2462", "line": 12, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 12, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2247", "line": 13, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 13, "endColumn": 12}, {"ruleId": "1940", "severity": 1, "message": "2463", "line": 17, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 17, "endColumn": 32}, {"ruleId": "1940", "severity": 1, "message": "2300", "line": 19, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 19, "endColumn": 33}, {"ruleId": "1940", "severity": 1, "message": "2464", "line": 34, "column": 11, "nodeType": "1942", "messageId": "1943", "endLine": 34, "endColumn": 31}, {"ruleId": "1940", "severity": 1, "message": "2318", "line": 34, "column": 33, "nodeType": "1942", "messageId": "1943", "endLine": 34, "endColumn": 44}, {"ruleId": "2465", "severity": 1, "message": "2466", "line": 96, "column": 2, "nodeType": "2467", "messageId": "2468", "endLine": 112, "endColumn": 4}, {"ruleId": "1940", "severity": 1, "message": "2469", "line": 133, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 133, "endColumn": 10}, {"ruleId": "1940", "severity": 1, "message": "2470", "line": 136, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 136, "endColumn": 12}, {"ruleId": "1940", "severity": 1, "message": "2348", "line": 137, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 137, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2050", "line": 138, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 138, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2471", "line": 140, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 140, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2345", "line": 141, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 141, "endColumn": 10}, {"ruleId": "1940", "severity": 1, "message": "2472", "line": 142, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 142, "endColumn": 8}, {"ruleId": "1940", "severity": 1, "message": "2473", "line": 145, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 145, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2474", "line": 146, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 146, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2475", "line": 147, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 147, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2476", "line": 148, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 148, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2477", "line": 149, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 149, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2478", "line": 150, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 150, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2355", "line": 151, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 151, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2354", "line": 152, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 152, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2353", "line": 153, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 153, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "2479", "line": 156, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 156, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2480", "line": 159, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 159, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2481", "line": 160, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 160, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2482", "line": 161, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 161, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2260", "line": 162, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 162, "endColumn": 8}, {"ruleId": "1940", "severity": 1, "message": "2220", "line": 163, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 163, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2483", "line": 166, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 166, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2484", "line": 167, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 167, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2434", "line": 169, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 169, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2485", "line": 174, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 174, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2072", "line": 175, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 175, "endColumn": 11}, {"ruleId": "1940", "severity": 1, "message": "2486", "line": 177, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 177, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2487", "line": 186, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 186, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2488", "line": 186, "column": 25, "nodeType": "1942", "messageId": "1943", "endLine": 186, "endColumn": 42}, {"ruleId": "1940", "severity": 1, "message": "2489", "line": 188, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 188, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2490", "line": 188, "column": 26, "nodeType": "1942", "messageId": "1943", "endLine": 188, "endColumn": 44}, {"ruleId": "2491", "severity": 1, "message": "2492", "line": 350, "column": 5, "nodeType": "2493", "messageId": "2494", "endLine": 350, "endColumn": 52}, {"ruleId": "1940", "severity": 1, "message": "2495", "line": 505, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 505, "endColumn": 31}, {"ruleId": "1940", "severity": 1, "message": "2496", "line": 575, "column": 7, "nodeType": "1942", "messageId": "1943", "endLine": 575, "endColumn": 21}, {"ruleId": "2044", "severity": 1, "message": "2497", "line": 742, "column": 5, "nodeType": "2046", "endLine": 742, "endColumn": 100, "suggestions": "2498"}, {"ruleId": "2044", "severity": 1, "message": "2499", "line": 760, "column": 5, "nodeType": "2046", "endLine": 760, "endColumn": 83, "suggestions": "2500"}, {"ruleId": "1940", "severity": 1, "message": "2501", "line": 940, "column": 7, "nodeType": "1942", "messageId": "1943", "endLine": 940, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2502", "line": 947, "column": 7, "nodeType": "1942", "messageId": "1943", "endLine": 947, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2452", "line": 1, "column": 17, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2503", "line": 1, "column": 28, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 36}, {"ruleId": "1940", "severity": 1, "message": "2504", "line": 4, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2258", "line": 14, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 14, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "2264", "line": 15, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 15, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2259", "line": 16, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 16, "endColumn": 32}, {"ruleId": "1940", "severity": 1, "message": "2225", "line": 17, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 17, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2268", "line": 20, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 20, "endColumn": 29}, {"ruleId": "1940", "severity": 1, "message": "2505", "line": 27, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 27, "endColumn": 29}, {"ruleId": "1940", "severity": 1, "message": "2503", "line": 1, "column": 17, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2506", "line": 2, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2182", "line": 2, "column": 19, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2247", "line": 2, "column": 27, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 37}, {"ruleId": "1940", "severity": 1, "message": "2245", "line": 2, "column": 39, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 42}, {"ruleId": "1940", "severity": 1, "message": "2461", "line": 2, "column": 44, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 58}, {"ruleId": "1940", "severity": 1, "message": "2210", "line": 2, "column": 60, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 73}, {"ruleId": "1940", "severity": 1, "message": "2332", "line": 2, "column": 74, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 84}, {"ruleId": "1940", "severity": 1, "message": "2507", "line": 3, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 3, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2508", "line": 4, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2258", "line": 98, "column": 13, "nodeType": "1942", "messageId": "1943", "endLine": 98, "endColumn": 32}, {"ruleId": "1940", "severity": 1, "message": "2264", "line": 99, "column": 13, "nodeType": "1942", "messageId": "1943", "endLine": 99, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2259", "line": 100, "column": 11, "nodeType": "1942", "messageId": "1943", "endLine": 100, "endColumn": 34}, {"ruleId": "1940", "severity": 1, "message": "2063", "line": 101, "column": 11, "nodeType": "1942", "messageId": "1943", "endLine": 101, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2260", "line": 102, "column": 11, "nodeType": "1942", "messageId": "1943", "endLine": 102, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2261", "line": 103, "column": 11, "nodeType": "1942", "messageId": "1943", "endLine": 103, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2080", "line": 104, "column": 11, "nodeType": "1942", "messageId": "1943", "endLine": 104, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2081", "line": 105, "column": 13, "nodeType": "1942", "messageId": "1943", "endLine": 105, "endColumn": 32}, {"ruleId": "1940", "severity": 1, "message": "2319", "line": 110, "column": 13, "nodeType": "1942", "messageId": "1943", "endLine": 110, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2226", "line": 113, "column": 13, "nodeType": "1942", "messageId": "1943", "endLine": 113, "endColumn": 29}, {"ruleId": "2044", "severity": 1, "message": "2509", "line": 172, "column": 12, "nodeType": "2046", "endLine": 172, "endColumn": 35, "suggestions": "2510"}, {"ruleId": "1940", "severity": 1, "message": "1947", "line": 1, "column": 27, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 34}, {"ruleId": "1940", "severity": 1, "message": "2175", "line": 4, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2276", "line": 7, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 7, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "1989", "line": 7, "column": 26, "nodeType": "1942", "messageId": "1943", "endLine": 7, "endColumn": 37}, {"ruleId": "1940", "severity": 1, "message": "2511", "line": 43, "column": 21, "nodeType": "1942", "messageId": "1943", "endLine": 43, "endColumn": 34}, {"ruleId": "2044", "severity": 1, "message": "2512", "line": 63, "column": 21, "nodeType": "2513", "endLine": 63, "endColumn": 111}, {"ruleId": "1940", "severity": 1, "message": "2514", "line": 2, "column": 25, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 39}, {"ruleId": "1940", "severity": 1, "message": "2515", "line": 4, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2174", "line": 5, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 5, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2516", "line": 11, "column": 7, "nodeType": "1942", "messageId": "1943", "endLine": 11, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2517", "line": 1, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2518", "line": 2, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "1978", "line": 1, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2518", "line": 2, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2519", "line": 2, "column": 27, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 40}, {"ruleId": "1940", "severity": 1, "message": "2247", "line": 2, "column": 23, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 33}, {"ruleId": "1940", "severity": 1, "message": "2520", "line": 3, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 3, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2520", "line": 3, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 3, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2245", "line": 2, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2247", "line": 2, "column": 15, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2332", "line": 2, "column": 50, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 60}, {"ruleId": "1940", "severity": 1, "message": "2507", "line": 29, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 29, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2249", "line": 34, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 34, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2485", "line": 64, "column": 29, "nodeType": "1942", "messageId": "1943", "endLine": 64, "endColumn": 43}, {"ruleId": "1940", "severity": 1, "message": "2521", "line": 72, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 72, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2522", "line": 74, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 74, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2523", "line": 77, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 77, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2524", "line": 78, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 78, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2525", "line": 97, "column": 15, "nodeType": "1942", "messageId": "1943", "endLine": 97, "endColumn": 22}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 106, "column": 34, "nodeType": "2095", "messageId": "2096", "endLine": 106, "endColumn": 36}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 154, "column": 44, "nodeType": "2095", "messageId": "2096", "endLine": 154, "endColumn": 46}, {"ruleId": "1940", "severity": 1, "message": "2526", "line": 173, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 173, "endColumn": 21}, {"ruleId": "2044", "severity": 1, "message": "2527", "line": 302, "column": 5, "nodeType": "2046", "endLine": 302, "endColumn": 50, "suggestions": "2528"}, {"ruleId": "2044", "severity": 1, "message": "2527", "line": 318, "column": 5, "nodeType": "2046", "endLine": 318, "endColumn": 18, "suggestions": "2529"}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 479, "column": 48, "nodeType": "2095", "messageId": "2096", "endLine": 479, "endColumn": 50}, {"ruleId": "1940", "severity": 1, "message": "2452", "line": 1, "column": 17, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2530", "line": 61, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 61, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2531", "line": 63, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 63, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2532", "line": 74, "column": 20, "nodeType": "1942", "messageId": "1943", "endLine": 74, "endColumn": 32}, {"ruleId": "1940", "severity": 1, "message": "2533", "line": 308, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 308, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2534", "line": 2, "column": 15, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2535", "line": 6, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 6, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2536", "line": 44, "column": 16, "nodeType": "1942", "messageId": "1943", "endLine": 44, "endColumn": 28}, {"ruleId": "2044", "severity": 1, "message": "2537", "line": 108, "column": 12, "nodeType": "2046", "endLine": 108, "endColumn": 14, "suggestions": "2538"}, {"ruleId": "2044", "severity": 1, "message": "2539", "line": 149, "column": 12, "nodeType": "2046", "endLine": 149, "endColumn": 26, "suggestions": "2540"}, {"ruleId": "2044", "severity": 1, "message": "2541", "line": 281, "column": 15, "nodeType": "2303", "endLine": 298, "endColumn": 10}, {"ruleId": "1940", "severity": 1, "message": "2542", "line": 311, "column": 15, "nodeType": "1942", "messageId": "1943", "endLine": 311, "endColumn": 28}, {"ruleId": "2044", "severity": 1, "message": "2543", "line": 406, "column": 13, "nodeType": "2303", "endLine": 406, "endColumn": 43}, {"ruleId": "1940", "severity": 1, "message": "2534", "line": 2, "column": 44, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 53}, {"ruleId": "1940", "severity": 1, "message": "2544", "line": 4, "column": 46, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 65}, {"ruleId": "1940", "severity": 1, "message": "1971", "line": 4, "column": 67, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 75}, {"ruleId": "1940", "severity": 1, "message": "2507", "line": 7, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 7, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2545", "line": 8, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 8, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2258", "line": 30, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 30, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2546", "line": 31, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 31, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2059", "line": 34, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 34, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2547", "line": 44, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 44, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2548", "line": 45, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 45, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2549", "line": 46, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 46, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2550", "line": 47, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 47, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2551", "line": 51, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 51, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2552", "line": 51, "column": 21, "nodeType": "1942", "messageId": "1943", "endLine": 51, "endColumn": 34}, {"ruleId": "1940", "severity": 1, "message": "2553", "line": 52, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 52, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2554", "line": 53, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 53, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2555", "line": 56, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 56, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2556", "line": 57, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 57, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2557", "line": 57, "column": 20, "nodeType": "1942", "messageId": "1943", "endLine": 57, "endColumn": 32}, {"ruleId": "2044", "severity": 1, "message": "2558", "line": 65, "column": 5, "nodeType": "2046", "endLine": 65, "endColumn": 7, "suggestions": "2559"}, {"ruleId": "1940", "severity": 1, "message": "2560", "line": 93, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 93, "endColumn": 32}, {"ruleId": "1940", "severity": 1, "message": "2561", "line": 97, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 97, "endColumn": 32}, {"ruleId": "1940", "severity": 1, "message": "2562", "line": 124, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 124, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2563", "line": 132, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 132, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2564", "line": 136, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 136, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2565", "line": 150, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 150, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2566", "line": 153, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 153, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "1947", "line": 5, "column": 28, "nodeType": "1942", "messageId": "1943", "endLine": 5, "endColumn": 35}, {"ruleId": "1940", "severity": 1, "message": "2567", "line": 8, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 8, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2305", "line": 85, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 85, "endColumn": 24}, {"ruleId": "2044", "severity": 1, "message": "2568", "line": 98, "column": 6, "nodeType": "2046", "endLine": 98, "endColumn": 8, "suggestions": "2569"}, {"ruleId": "2044", "severity": 1, "message": "2570", "line": 121, "column": 6, "nodeType": "2046", "endLine": 121, "endColumn": 32, "suggestions": "2571"}, {"ruleId": "2044", "severity": 1, "message": "2311", "line": 125, "column": 6, "nodeType": "2046", "endLine": 125, "endColumn": 40, "suggestions": "2572"}, {"ruleId": "2044", "severity": 1, "message": "2099", "line": 125, "column": 7, "nodeType": "2095", "endLine": 125, "endColumn": 39}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 125, "column": 35, "nodeType": "2095", "messageId": "2096", "endLine": 125, "endColumn": 37}, {"ruleId": "2044", "severity": 1, "message": "2573", "line": 148, "column": 6, "nodeType": "2046", "endLine": 148, "endColumn": 33, "suggestions": "2574"}, {"ruleId": "2044", "severity": 1, "message": "2099", "line": 148, "column": 7, "nodeType": "2100", "endLine": 148, "endColumn": 32}, {"ruleId": "1940", "severity": 1, "message": "2575", "line": 156, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 156, "endColumn": 29}, {"ruleId": "1940", "severity": 1, "message": "2576", "line": 2, "column": 14, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2577", "line": 16, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 16, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2578", "line": 19, "column": 11, "nodeType": "1942", "messageId": "1943", "endLine": 19, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2579", "line": 22, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 22, "endColumn": 20}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 43, "column": 100, "nodeType": "2095", "messageId": "2096", "endLine": 43, "endColumn": 102}, {"ruleId": "1940", "severity": 1, "message": "2580", "line": 4, "column": 23, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 30}, {"ruleId": "1940", "severity": 1, "message": "2581", "line": 4, "column": 32, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 45}, {"ruleId": "1940", "severity": 1, "message": "2582", "line": 10, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 10, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2583", "line": 65, "column": 12, "nodeType": "1942", "messageId": "1943", "endLine": 65, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2584", "line": 65, "column": 24, "nodeType": "1942", "messageId": "1943", "endLine": 65, "endColumn": 37}, {"ruleId": "1940", "severity": 1, "message": "2585", "line": 78, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 78, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2586", "line": 78, "column": 24, "nodeType": "1942", "messageId": "1943", "endLine": 78, "endColumn": 39}, {"ruleId": "2044", "severity": 1, "message": "2587", "line": 120, "column": 6, "nodeType": "2046", "endLine": 120, "endColumn": 8, "suggestions": "2588"}, {"ruleId": "1940", "severity": 1, "message": "2589", "line": 157, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 157, "endColumn": 32}, {"ruleId": "1940", "severity": 1, "message": "2590", "line": 280, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 280, "endColumn": 29}, {"ruleId": "1940", "severity": 1, "message": "2591", "line": 296, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 296, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2592", "line": 461, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 461, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2593", "line": 462, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 462, "endColumn": 20}, {"ruleId": "2044", "severity": 1, "message": "2594", "line": 467, "column": 3, "nodeType": "2046", "endLine": 467, "endColumn": 5, "suggestions": "2595"}, {"ruleId": "1940", "severity": 1, "message": "2596", "line": 1, "column": 17, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2326", "line": 2, "column": 38, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 42}, {"ruleId": "1940", "severity": 1, "message": "1973", "line": 2, "column": 64, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 78}, {"ruleId": "1940", "severity": 1, "message": "2328", "line": 2, "column": 80, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 91}, {"ruleId": "1940", "severity": 1, "message": "2329", "line": 2, "column": 93, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 103}, {"ruleId": "1940", "severity": 1, "message": "2330", "line": 2, "column": 105, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 111}, {"ruleId": "1940", "severity": 1, "message": "2331", "line": 2, "column": 113, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 121}, {"ruleId": "1940", "severity": 1, "message": "2597", "line": 2, "column": 123, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 140}, {"ruleId": "1940", "severity": 1, "message": "2205", "line": 2, "column": 142, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 158}, {"ruleId": "1940", "severity": 1, "message": "2598", "line": 2, "column": 160, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 166}, {"ruleId": "1940", "severity": 1, "message": "2599", "line": 2, "column": 168, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 180}, {"ruleId": "1940", "severity": 1, "message": "2600", "line": 2, "column": 182, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 199}, {"ruleId": "1940", "severity": 1, "message": "2601", "line": 4, "column": 26, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 49}, {"ruleId": "1940", "severity": 1, "message": "2602", "line": 4, "column": 51, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 71}, {"ruleId": "1940", "severity": 1, "message": "2603", "line": 4, "column": 73, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 91}, {"ruleId": "1940", "severity": 1, "message": "2604", "line": 5, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 5, "endColumn": 31}, {"ruleId": "1940", "severity": 1, "message": "2605", "line": 13, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 13, "endColumn": 12}, {"ruleId": "1940", "severity": 1, "message": "2606", "line": 14, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 14, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2607", "line": 15, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 15, "endColumn": 10}, {"ruleId": "1940", "severity": 1, "message": "1971", "line": 16, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 16, "endColumn": 10}, {"ruleId": "1940", "severity": 1, "message": "2608", "line": 17, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 17, "endColumn": 7}, {"ruleId": "1940", "severity": 1, "message": "2609", "line": 24, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 24, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "2610", "line": 25, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 25, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2611", "line": 26, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 26, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2612", "line": 27, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 27, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2613", "line": 28, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 28, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2614", "line": 29, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 29, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2615", "line": 30, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 30, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2616", "line": 40, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 40, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2617", "line": 41, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 41, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2618", "line": 43, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 43, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2619", "line": 45, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 45, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2313", "line": 47, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 47, "endColumn": 12}, {"ruleId": "1940", "severity": 1, "message": "2620", "line": 94, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 94, "endColumn": 19}, {"ruleId": "2044", "severity": 1, "message": "2621", "line": 125, "column": 5, "nodeType": "2046", "endLine": 125, "endColumn": 7, "suggestions": "2622"}, {"ruleId": "1940", "severity": 1, "message": "2623", "line": 145, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 145, "endColumn": 30}, {"ruleId": "1940", "severity": 1, "message": "2624", "line": 162, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 162, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2625", "line": 165, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 165, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2626", "line": 170, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 170, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2627", "line": 211, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 211, "endColumn": 29}, {"ruleId": "1940", "severity": 1, "message": "2628", "line": 214, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 214, "endColumn": 33}, {"ruleId": "1940", "severity": 1, "message": "2629", "line": 227, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 227, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2630", "line": 228, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 228, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2631", "line": 228, "column": 15, "nodeType": "1942", "messageId": "1943", "endLine": 228, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2632", "line": 229, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 229, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2633", "line": 229, "column": 20, "nodeType": "1942", "messageId": "1943", "endLine": 229, "endColumn": 32}, {"ruleId": "1940", "severity": 1, "message": "2026", "line": 245, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 245, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2027", "line": 245, "column": 16, "nodeType": "1942", "messageId": "1943", "endLine": 245, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2634", "line": 247, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 247, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2635", "line": 261, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 261, "endColumn": 36}, {"ruleId": "2044", "severity": 1, "message": "2636", "line": 281, "column": 4, "nodeType": "2046", "endLine": 281, "endColumn": 6, "suggestions": "2637"}, {"ruleId": "1940", "severity": 1, "message": "2635", "line": 334, "column": 12, "nodeType": "1942", "messageId": "1943", "endLine": 334, "endColumn": 38}, {"ruleId": "1940", "severity": 1, "message": "2638", "line": 347, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 347, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2639", "line": 347, "column": 27, "nodeType": "1942", "messageId": "1943", "endLine": 347, "endColumn": 45}, {"ruleId": "1940", "severity": 1, "message": "2596", "line": 1, "column": 17, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2326", "line": 2, "column": 38, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 42}, {"ruleId": "1940", "severity": 1, "message": "1973", "line": 2, "column": 64, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 78}, {"ruleId": "1940", "severity": 1, "message": "2328", "line": 2, "column": 80, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 91}, {"ruleId": "1940", "severity": 1, "message": "2329", "line": 2, "column": 93, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 103}, {"ruleId": "1940", "severity": 1, "message": "2330", "line": 2, "column": 105, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 111}, {"ruleId": "1940", "severity": 1, "message": "2331", "line": 2, "column": 113, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 121}, {"ruleId": "1940", "severity": 1, "message": "2597", "line": 2, "column": 123, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 140}, {"ruleId": "1940", "severity": 1, "message": "2205", "line": 2, "column": 142, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 158}, {"ruleId": "1940", "severity": 1, "message": "2598", "line": 2, "column": 160, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 166}, {"ruleId": "1940", "severity": 1, "message": "2333", "line": 2, "column": 168, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 175}, {"ruleId": "1940", "severity": 1, "message": "2601", "line": 4, "column": 26, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 49}, {"ruleId": "1940", "severity": 1, "message": "2602", "line": 4, "column": 51, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 71}, {"ruleId": "1940", "severity": 1, "message": "2603", "line": 4, "column": 73, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 91}, {"ruleId": "1940", "severity": 1, "message": "2604", "line": 5, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 5, "endColumn": 31}, {"ruleId": "1940", "severity": 1, "message": "2605", "line": 8, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 8, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2606", "line": 9, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2607", "line": 10, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 10, "endColumn": 11}, {"ruleId": "1940", "severity": 1, "message": "2608", "line": 11, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 11, "endColumn": 10}, {"ruleId": "1940", "severity": 1, "message": "1971", "line": 12, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 12, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2640", "line": 13, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 13, "endColumn": 12}, {"ruleId": "1940", "severity": 1, "message": "2641", "line": 14, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 14, "endColumn": 12}, {"ruleId": "1940", "severity": 1, "message": "2642", "line": 15, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 15, "endColumn": 12}, {"ruleId": "1940", "severity": 1, "message": "2643", "line": 22, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 22, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2644", "line": 31, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 31, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2296", "line": 32, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 32, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2616", "line": 35, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 35, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2617", "line": 36, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 36, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2618", "line": 38, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 38, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2645", "line": 39, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 39, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2646", "line": 40, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 40, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2647", "line": 42, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 42, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2648", "line": 43, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 43, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2649", "line": 44, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 44, "endColumn": 29}, {"ruleId": "1940", "severity": 1, "message": "2650", "line": 45, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 45, "endColumn": 32}, {"ruleId": "1940", "severity": 1, "message": "2651", "line": 46, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 46, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2652", "line": 47, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 47, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2653", "line": 48, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 48, "endColumn": 29}, {"ruleId": "1940", "severity": 1, "message": "2654", "line": 49, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 49, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2655", "line": 50, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 50, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2656", "line": 51, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 51, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2521", "line": 58, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 58, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2578", "line": 60, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 60, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2620", "line": 75, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 75, "endColumn": 19}, {"ruleId": "2044", "severity": 1, "message": "2573", "line": 87, "column": 5, "nodeType": "2046", "endLine": 87, "endColumn": 45, "suggestions": "2657"}, {"ruleId": "2044", "severity": 1, "message": "2099", "line": 87, "column": 6, "nodeType": "2148", "endLine": 87, "endColumn": 44}, {"ruleId": "1940", "severity": 1, "message": "2658", "line": 106, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 106, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2659", "line": 106, "column": 23, "nodeType": "1942", "messageId": "1943", "endLine": 106, "endColumn": 38}, {"ruleId": "1940", "severity": 1, "message": "2660", "line": 107, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 107, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2661", "line": 107, "column": 15, "nodeType": "1942", "messageId": "1943", "endLine": 107, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2662", "line": 108, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 108, "endColumn": 11}, {"ruleId": "1940", "severity": 1, "message": "2219", "line": 109, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 109, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2663", "line": 109, "column": 18, "nodeType": "1942", "messageId": "1943", "endLine": 109, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "2664", "line": 110, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 110, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2624", "line": 115, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 115, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2626", "line": 119, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 119, "endColumn": 25}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 134, "column": 11, "nodeType": "2095", "messageId": "2096", "endLine": 134, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2333", "line": 2, "column": 64, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 71}, {"ruleId": "1940", "severity": 1, "message": "2665", "line": 4, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 32}, {"ruleId": "1940", "severity": 1, "message": "2666", "line": 5, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 5, "endColumn": 30}, {"ruleId": "1940", "severity": 1, "message": "2667", "line": 10, "column": 30, "nodeType": "1942", "messageId": "1943", "endLine": 10, "endColumn": 39}, {"ruleId": "1940", "severity": 1, "message": "2247", "line": 2, "column": 15, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2599", "line": 2, "column": 27, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 39}, {"ruleId": "1940", "severity": 1, "message": "2600", "line": 2, "column": 41, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 58}, {"ruleId": "1940", "severity": 1, "message": "2205", "line": 2, "column": 72, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 88}, {"ruleId": "1940", "severity": 1, "message": "2598", "line": 2, "column": 90, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 96}, {"ruleId": "1940", "severity": 1, "message": "2668", "line": 9, "column": 11, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 31}, {"ruleId": "1940", "severity": 1, "message": "2598", "line": 6, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 6, "endColumn": 8}, {"ruleId": "1940", "severity": 1, "message": "2330", "line": 9, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 8}, {"ruleId": "1940", "severity": 1, "message": "2331", "line": 10, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 10, "endColumn": 10}, {"ruleId": "1940", "severity": 1, "message": "2328", "line": 11, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 11, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2333", "line": 12, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 12, "endColumn": 9}, {"ruleId": "1940", "severity": 1, "message": "2669", "line": 19, "column": 11, "nodeType": "1942", "messageId": "1943", "endLine": 19, "endColumn": 32}, {"ruleId": "1940", "severity": 1, "message": "2365", "line": 35, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 35, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2670", "line": 37, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 37, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2464", "line": 38, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 38, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2318", "line": 39, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 39, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2671", "line": 40, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 40, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2221", "line": 42, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 42, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2226", "line": 48, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 48, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2672", "line": 55, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 55, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2673", "line": 56, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 56, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2674", "line": 57, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 57, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2675", "line": 86, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 86, "endColumn": 31}, {"ruleId": "1940", "severity": 1, "message": "2676", "line": 90, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 90, "endColumn": 31}, {"ruleId": "1940", "severity": 1, "message": "2677", "line": 95, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 95, "endColumn": 33}, {"ruleId": "2044", "severity": 1, "message": "2678", "line": 195, "column": 5, "nodeType": "2046", "endLine": 195, "endColumn": 30, "suggestions": "2679"}, {"ruleId": "1940", "severity": 1, "message": "2245", "line": 3, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 3, "endColumn": 5}, {"ruleId": "1940", "severity": 1, "message": "2247", "line": 4, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 12}, {"ruleId": "1940", "severity": 1, "message": "2329", "line": 9, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 12}, {"ruleId": "1940", "severity": 1, "message": "2245", "line": 2, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2247", "line": 2, "column": 23, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 33}, {"ruleId": "1940", "severity": 1, "message": "2326", "line": 2, "column": 38, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 42}, {"ruleId": "1940", "severity": 1, "message": "2333", "line": 2, "column": 64, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 71}, {"ruleId": "1940", "severity": 1, "message": "2665", "line": 4, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 32}, {"ruleId": "1940", "severity": 1, "message": "2666", "line": 5, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 5, "endColumn": 30}, {"ruleId": "1940", "severity": 1, "message": "2680", "line": 9, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2681", "line": 9, "column": 23, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 30}, {"ruleId": "1940", "severity": 1, "message": "2667", "line": 9, "column": 32, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 41}, {"ruleId": "1940", "severity": 1, "message": "2682", "line": 9, "column": 43, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 51}, {"ruleId": "1940", "severity": 1, "message": "2683", "line": 9, "column": 53, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 63}, {"ruleId": "1940", "severity": 1, "message": "2684", "line": 9, "column": 65, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 77}, {"ruleId": "1940", "severity": 1, "message": "2685", "line": 9, "column": 79, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 90}, {"ruleId": "1940", "severity": 1, "message": "2686", "line": 9, "column": 92, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 102}, {"ruleId": "1940", "severity": 1, "message": "2687", "line": 9, "column": 104, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 116}, {"ruleId": "1940", "severity": 1, "message": "2688", "line": 9, "column": 118, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 129}, {"ruleId": "1940", "severity": 1, "message": "2689", "line": 9, "column": 131, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 140}, {"ruleId": "1940", "severity": 1, "message": "2690", "line": 15, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 15, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2348", "line": 16, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 16, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2691", "line": 17, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 17, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2472", "line": 18, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 18, "endColumn": 8}, {"ruleId": "1940", "severity": 1, "message": "2692", "line": 19, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 19, "endColumn": 11}, {"ruleId": "1940", "severity": 1, "message": "2050", "line": 20, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 20, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2471", "line": 23, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 23, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2693", "line": 24, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 24, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2694", "line": 25, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 25, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2695", "line": 26, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 26, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2696", "line": 27, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 27, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2697", "line": 28, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 28, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2698", "line": 29, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 29, "endColumn": 12}, {"ruleId": "1940", "severity": 1, "message": "2699", "line": 30, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 30, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2521", "line": 34, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 34, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2620", "line": 62, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 62, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2676", "line": 82, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 82, "endColumn": 31}, {"ruleId": "1940", "severity": 1, "message": "2700", "line": 83, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 83, "endColumn": 35}, {"ruleId": "1940", "severity": 1, "message": "2596", "line": 1, "column": 17, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2326", "line": 3, "column": 38, "nodeType": "1942", "messageId": "1943", "endLine": 3, "endColumn": 42}, {"ruleId": "1940", "severity": 1, "message": "1973", "line": 3, "column": 64, "nodeType": "1942", "messageId": "1943", "endLine": 3, "endColumn": 78}, {"ruleId": "1940", "severity": 1, "message": "2328", "line": 3, "column": 80, "nodeType": "1942", "messageId": "1943", "endLine": 3, "endColumn": 91}, {"ruleId": "1940", "severity": 1, "message": "2329", "line": 3, "column": 93, "nodeType": "1942", "messageId": "1943", "endLine": 3, "endColumn": 103}, {"ruleId": "1940", "severity": 1, "message": "2330", "line": 3, "column": 105, "nodeType": "1942", "messageId": "1943", "endLine": 3, "endColumn": 111}, {"ruleId": "1940", "severity": 1, "message": "2331", "line": 3, "column": 113, "nodeType": "1942", "messageId": "1943", "endLine": 3, "endColumn": 121}, {"ruleId": "1940", "severity": 1, "message": "2597", "line": 3, "column": 123, "nodeType": "1942", "messageId": "1943", "endLine": 3, "endColumn": 140}, {"ruleId": "1940", "severity": 1, "message": "2205", "line": 3, "column": 142, "nodeType": "1942", "messageId": "1943", "endLine": 3, "endColumn": 158}, {"ruleId": "1940", "severity": 1, "message": "2598", "line": 3, "column": 160, "nodeType": "1942", "messageId": "1943", "endLine": 3, "endColumn": 166}, {"ruleId": "1940", "severity": 1, "message": "2601", "line": 5, "column": 26, "nodeType": "1942", "messageId": "1943", "endLine": 5, "endColumn": 49}, {"ruleId": "1940", "severity": 1, "message": "2602", "line": 5, "column": 51, "nodeType": "1942", "messageId": "1943", "endLine": 5, "endColumn": 71}, {"ruleId": "1940", "severity": 1, "message": "2603", "line": 5, "column": 73, "nodeType": "1942", "messageId": "1943", "endLine": 5, "endColumn": 91}, {"ruleId": "1940", "severity": 1, "message": "2604", "line": 6, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 6, "endColumn": 31}, {"ruleId": "1940", "severity": 1, "message": "2605", "line": 8, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 8, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2606", "line": 9, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2607", "line": 10, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 10, "endColumn": 11}, {"ruleId": "1940", "severity": 1, "message": "2608", "line": 11, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 11, "endColumn": 8}, {"ruleId": "1940", "severity": 1, "message": "2616", "line": 19, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 19, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2618", "line": 22, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 22, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2701", "line": 24, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 24, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2702", "line": 25, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 25, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2703", "line": 26, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 26, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2704", "line": 27, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 27, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2521", "line": 31, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 31, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2620", "line": 36, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 36, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2705", "line": 104, "column": 21, "nodeType": "1942", "messageId": "1943", "endLine": 104, "endColumn": 33}, {"ruleId": "1940", "severity": 1, "message": "2706", "line": 105, "column": 24, "nodeType": "1942", "messageId": "1943", "endLine": 105, "endColumn": 40}, {"ruleId": "1940", "severity": 1, "message": "2623", "line": 120, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 120, "endColumn": 31}, {"ruleId": "1940", "severity": 1, "message": "2624", "line": 154, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 154, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2707", "line": 157, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 157, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2326", "line": 3, "column": 38, "nodeType": "1942", "messageId": "1943", "endLine": 3, "endColumn": 42}, {"ruleId": "1940", "severity": 1, "message": "2333", "line": 3, "column": 56, "nodeType": "1942", "messageId": "1943", "endLine": 3, "endColumn": 63}, {"ruleId": "1940", "severity": 1, "message": "2222", "line": 13, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 13, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2345", "line": 14, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 14, "endColumn": 10}, {"ruleId": "1940", "severity": 1, "message": "2346", "line": 15, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 15, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2031", "line": 16, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 16, "endColumn": 11}, {"ruleId": "1940", "severity": 1, "message": "2033", "line": 18, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 18, "endColumn": 9}, {"ruleId": "1940", "severity": 1, "message": "2034", "line": 19, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 19, "endColumn": 12}, {"ruleId": "1940", "severity": 1, "message": "2035", "line": 20, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 20, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2036", "line": 21, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 21, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2347", "line": 22, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 22, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2348", "line": 23, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 23, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2349", "line": 24, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 24, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2050", "line": 25, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 25, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2708", "line": 37, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 37, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2387", "line": 39, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 39, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2709", "line": 41, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 41, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2710", "line": 45, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 45, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "2711", "line": 49, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 49, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2712", "line": 49, "column": 25, "nodeType": "1942", "messageId": "1943", "endLine": 49, "endColumn": 42}, {"ruleId": "1940", "severity": 1, "message": "2713", "line": 50, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 50, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2714", "line": 50, "column": 21, "nodeType": "1942", "messageId": "1943", "endLine": 50, "endColumn": 34}, {"ruleId": "1940", "severity": 1, "message": "2715", "line": 51, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 51, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2716", "line": 51, "column": 26, "nodeType": "1942", "messageId": "1943", "endLine": 51, "endColumn": 44}, {"ruleId": "1940", "severity": 1, "message": "2717", "line": 52, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 52, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "2718", "line": 52, "column": 30, "nodeType": "1942", "messageId": "1943", "endLine": 52, "endColumn": 52}, {"ruleId": "1940", "severity": 1, "message": "2719", "line": 53, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 53, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2720", "line": 53, "column": 27, "nodeType": "1942", "messageId": "1943", "endLine": 53, "endColumn": 46}, {"ruleId": "1940", "severity": 1, "message": "2721", "line": 3, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 3, "endColumn": 8}, {"ruleId": "1940", "severity": 1, "message": "2722", "line": 4, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2723", "line": 5, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 5, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2724", "line": 6, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 6, "endColumn": 10}, {"ruleId": "1940", "severity": 1, "message": "2506", "line": 9, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 9}, {"ruleId": "1940", "severity": 1, "message": "2203", "line": 17, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 17, "endColumn": 12}, {"ruleId": "1940", "severity": 1, "message": "2204", "line": 18, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 18, "endColumn": 7}, {"ruleId": "1940", "severity": 1, "message": "2205", "line": 19, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 19, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2206", "line": 20, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 20, "endColumn": 7}, {"ruleId": "1940", "severity": 1, "message": "2207", "line": 23, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 23, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2208", "line": 24, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 24, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2209", "line": 25, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 25, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2333", "line": 26, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 26, "endColumn": 9}, {"ruleId": "1940", "severity": 1, "message": "2405", "line": 43, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 43, "endColumn": 35}, {"ruleId": "1940", "severity": 1, "message": "2725", "line": 44, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 44, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2726", "line": 50, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 50, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2727", "line": 51, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 51, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2728", "line": 53, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 53, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2729", "line": 54, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 54, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2730", "line": 55, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 55, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2731", "line": 56, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 56, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2258", "line": 59, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 59, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2546", "line": 60, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 60, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2216", "line": 62, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 62, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2217", "line": 68, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 68, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2218", "line": 69, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 69, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2219", "line": 75, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 75, "endColumn": 10}, {"ruleId": "1940", "severity": 1, "message": "2732", "line": 77, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 77, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2060", "line": 80, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 80, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2061", "line": 81, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 81, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2059", "line": 82, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 82, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2733", "line": 83, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 83, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2734", "line": 84, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 84, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2735", "line": 85, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 85, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2232", "line": 87, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 87, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2230", "line": 89, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 89, "endColumn": 11}, {"ruleId": "1940", "severity": 1, "message": "2234", "line": 91, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 91, "endColumn": 10}, {"ruleId": "1940", "severity": 1, "message": "2736", "line": 92, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 92, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2227", "line": 94, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 94, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2348", "line": 101, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 101, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2347", "line": 101, "column": 22, "nodeType": "1942", "messageId": "1943", "endLine": 101, "endColumn": 36}, {"ruleId": "1940", "severity": 1, "message": "2050", "line": 102, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 102, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2349", "line": 102, "column": 26, "nodeType": "1942", "messageId": "1943", "endLine": 102, "endColumn": 44}, {"ruleId": "1940", "severity": 1, "message": "2737", "line": 103, "column": 17, "nodeType": "1942", "messageId": "1943", "endLine": 103, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2738", "line": 104, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 104, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2739", "line": 105, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 105, "endColumn": 12}, {"ruleId": "1940", "severity": 1, "message": "2740", "line": 105, "column": 14, "nodeType": "1942", "messageId": "1943", "endLine": 105, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2228", "line": 106, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 106, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2741", "line": 106, "column": 17, "nodeType": "1942", "messageId": "1943", "endLine": 106, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2432", "line": 107, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 107, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2742", "line": 107, "column": 23, "nodeType": "1942", "messageId": "1943", "endLine": 107, "endColumn": 38}, {"ruleId": "1940", "severity": 1, "message": "2674", "line": 118, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 118, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2743", "line": 118, "column": 17, "nodeType": "1942", "messageId": "1943", "endLine": 118, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2744", "line": 125, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 125, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2745", "line": 125, "column": 26, "nodeType": "1942", "messageId": "1943", "endLine": 125, "endColumn": 44}, {"ruleId": "2044", "severity": 1, "message": "2746", "line": 148, "column": 5, "nodeType": "2046", "endLine": 148, "endColumn": 60, "suggestions": "2747"}, {"ruleId": "1940", "severity": 1, "message": "2748", "line": 151, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 151, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2749", "line": 163, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 163, "endColumn": 24}, {"ruleId": "2044", "severity": 1, "message": "2750", "line": 167, "column": 5, "nodeType": "2046", "endLine": 167, "endColumn": 60, "suggestions": "2751"}, {"ruleId": "1940", "severity": 1, "message": "2752", "line": 169, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 169, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2235", "line": 203, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 203, "endColumn": 30}, {"ruleId": "1940", "severity": 1, "message": "2236", "line": 207, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 207, "endColumn": 29}, {"ruleId": "1940", "severity": 1, "message": "2534", "line": 2, "column": 56, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 65}, {"ruleId": "1940", "severity": 1, "message": "2331", "line": 2, "column": 67, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 75}, {"ruleId": "1940", "severity": 1, "message": "2182", "line": 2, "column": 77, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 83}, {"ruleId": "1940", "severity": 1, "message": "2544", "line": 13, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 13, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2753", "line": 47, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 47, "endColumn": 48}, {"ruleId": "1940", "severity": 1, "message": "2428", "line": 59, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 59, "endColumn": 39}, {"ruleId": "1940", "severity": 1, "message": "2754", "line": 68, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 68, "endColumn": 41}, {"ruleId": "1940", "severity": 1, "message": "2755", "line": 74, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 74, "endColumn": 42}, {"ruleId": "1940", "severity": 1, "message": "2596", "line": 1, "column": 17, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2452", "line": 1, "column": 38, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 47}, {"ruleId": "1940", "severity": 1, "message": "2326", "line": 2, "column": 38, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 42}, {"ruleId": "1940", "severity": 1, "message": "1973", "line": 2, "column": 64, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 78}, {"ruleId": "1940", "severity": 1, "message": "2329", "line": 2, "column": 93, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 103}, {"ruleId": "1940", "severity": 1, "message": "2597", "line": 2, "column": 123, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 140}, {"ruleId": "1940", "severity": 1, "message": "2205", "line": 2, "column": 142, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 158}, {"ruleId": "1940", "severity": 1, "message": "2598", "line": 2, "column": 160, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 166}, {"ruleId": "1940", "severity": 1, "message": "2601", "line": 4, "column": 26, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 49}, {"ruleId": "1940", "severity": 1, "message": "2602", "line": 4, "column": 51, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 71}, {"ruleId": "1940", "severity": 1, "message": "2603", "line": 4, "column": 73, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 91}, {"ruleId": "1940", "severity": 1, "message": "2604", "line": 5, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 5, "endColumn": 31}, {"ruleId": "1940", "severity": 1, "message": "2756", "line": 23, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 23, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2069", "line": 24, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 24, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2757", "line": 26, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 26, "endColumn": 29}, {"ruleId": "1940", "severity": 1, "message": "2065", "line": 27, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 27, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2758", "line": 28, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 28, "endColumn": 29}, {"ruleId": "1940", "severity": 1, "message": "2759", "line": 29, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 29, "endColumn": 31}, {"ruleId": "1940", "severity": 1, "message": "2372", "line": 85, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 85, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2404", "line": 4, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2060", "line": 11, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 11, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2061", "line": 12, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 12, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2059", "line": 13, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 13, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2410", "line": 21, "column": 21, "nodeType": "1942", "messageId": "1943", "endLine": 21, "endColumn": 34}, {"ruleId": "1940", "severity": 1, "message": "2411", "line": 22, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 22, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2412", "line": 22, "column": 33, "nodeType": "1942", "messageId": "1943", "endLine": 22, "endColumn": 44}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 82, "column": 22, "nodeType": "2095", "messageId": "2096", "endLine": 82, "endColumn": 24}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 82, "column": 53, "nodeType": "2095", "messageId": "2096", "endLine": 82, "endColumn": 55}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 85, "column": 36, "nodeType": "2095", "messageId": "2096", "endLine": 85, "endColumn": 38}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 85, "column": 63, "nodeType": "2095", "messageId": "2096", "endLine": 85, "endColumn": 65}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 88, "column": 36, "nodeType": "2095", "messageId": "2096", "endLine": 88, "endColumn": 38}, {"ruleId": "1940", "severity": 1, "message": "2414", "line": 95, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 95, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2415", "line": 96, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 96, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2416", "line": 99, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 99, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2417", "line": 100, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 100, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2418", "line": 108, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 108, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2419", "line": 128, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 128, "endColumn": 16}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 268, "column": 45, "nodeType": "2095", "messageId": "2096", "endLine": 268, "endColumn": 47}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 268, "column": 104, "nodeType": "2095", "messageId": "2096", "endLine": 268, "endColumn": 106}, {"ruleId": "1940", "severity": 1, "message": "2760", "line": 1, "column": 17, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2460", "line": 4, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2534", "line": 8, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 8, "endColumn": 11}, {"ruleId": "1940", "severity": 1, "message": "2761", "line": 15, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 15, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "2762", "line": 15, "column": 30, "nodeType": "1942", "messageId": "1943", "endLine": 15, "endColumn": 44}, {"ruleId": "1940", "severity": 1, "message": "2763", "line": 15, "column": 46, "nodeType": "1942", "messageId": "1943", "endLine": 15, "endColumn": 60}, {"ruleId": "1940", "severity": 1, "message": "2764", "line": 15, "column": 62, "nodeType": "1942", "messageId": "1943", "endLine": 15, "endColumn": 78}, {"ruleId": "1940", "severity": 1, "message": "2765", "line": 15, "column": 80, "nodeType": "1942", "messageId": "1943", "endLine": 15, "endColumn": 96}, {"ruleId": "1940", "severity": 1, "message": "2766", "line": 17, "column": 29, "nodeType": "1942", "messageId": "1943", "endLine": 17, "endColumn": 33}, {"ruleId": "1940", "severity": 1, "message": "2767", "line": 17, "column": 35, "nodeType": "1942", "messageId": "1943", "endLine": 17, "endColumn": 47}, {"ruleId": "1940", "severity": 1, "message": "2211", "line": 17, "column": 49, "nodeType": "1942", "messageId": "1943", "endLine": 17, "endColumn": 55}, {"ruleId": "1940", "severity": 1, "message": "2768", "line": 22, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 22, "endColumn": 11}, {"ruleId": "1940", "severity": 1, "message": "2769", "line": 58, "column": 13, "nodeType": "1942", "messageId": "1943", "endLine": 58, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2469", "line": 65, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 65, "endColumn": 10}, {"ruleId": "1940", "severity": 1, "message": "2770", "line": 66, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 66, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2472", "line": 72, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 72, "endColumn": 8}, {"ruleId": "1940", "severity": 1, "message": "2471", "line": 73, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 73, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2694", "line": 74, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 74, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2348", "line": 75, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 75, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2771", "line": 76, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 76, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2479", "line": 77, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 77, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2772", "line": 78, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 78, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2480", "line": 79, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 79, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2475", "line": 80, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 80, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2773", "line": 81, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 81, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2696", "line": 82, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 82, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2220", "line": 83, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 83, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2215", "line": 84, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 84, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2774", "line": 87, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 87, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2775", "line": 89, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 89, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2221", "line": 91, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 91, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2226", "line": 93, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 93, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2776", "line": 99, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 99, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2777", "line": 99, "column": 21, "nodeType": "1942", "messageId": "1943", "endLine": 99, "endColumn": 33}, {"ruleId": "1940", "severity": 1, "message": "2778", "line": 103, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 103, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2779", "line": 103, "column": 26, "nodeType": "1942", "messageId": "1943", "endLine": 103, "endColumn": 44}, {"ruleId": "1940", "severity": 1, "message": "2780", "line": 161, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 161, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2781", "line": 164, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 164, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "2782", "line": 170, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 170, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2783", "line": 173, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 173, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2784", "line": 183, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 183, "endColumn": 21}, {"ruleId": "2044", "severity": 1, "message": "2785", "line": 339, "column": 5, "nodeType": "2046", "endLine": 339, "endColumn": 18, "suggestions": "2786"}, {"ruleId": "1940", "severity": 1, "message": "2787", "line": 341, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 341, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2482", "line": 593, "column": 33, "nodeType": "1942", "messageId": "1943", "endLine": 593, "endColumn": 47}, {"ruleId": "1940", "severity": 1, "message": "2318", "line": 593, "column": 49, "nodeType": "1942", "messageId": "1943", "endLine": 593, "endColumn": 60}, {"ruleId": "1940", "severity": 1, "message": "2260", "line": 593, "column": 62, "nodeType": "1942", "messageId": "1943", "endLine": 593, "endColumn": 67}, {"ruleId": "1940", "severity": 1, "message": "2220", "line": 593, "column": 69, "nodeType": "1942", "messageId": "1943", "endLine": 593, "endColumn": 85}, {"ruleId": "1940", "severity": 1, "message": "2788", "line": 783, "column": 7, "nodeType": "1942", "messageId": "1943", "endLine": 783, "endColumn": 17}, {"ruleId": "2044", "severity": 1, "message": "2789", "line": 924, "column": 3, "nodeType": "2046", "endLine": 924, "endColumn": 19, "suggestions": "2790"}, {"ruleId": "1940", "severity": 1, "message": "2791", "line": 1023, "column": 15, "nodeType": "1942", "messageId": "1943", "endLine": 1023, "endColumn": 33}, {"ruleId": "1940", "severity": 1, "message": "2792", "line": 1032, "column": 15, "nodeType": "1942", "messageId": "1943", "endLine": 1032, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2462", "line": 11, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 11, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2404", "line": 14, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 14, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2793", "line": 67, "column": 11, "nodeType": "1942", "messageId": "1943", "endLine": 67, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2794", "line": 180, "column": 86, "nodeType": "1942", "messageId": "1943", "endLine": 180, "endColumn": 101}, {"ruleId": "1940", "severity": 1, "message": "2480", "line": 184, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 184, "endColumn": 24}, {"ruleId": "2044", "severity": 1, "message": "2795", "line": 563, "column": 5, "nodeType": "2046", "endLine": 563, "endColumn": 40, "suggestions": "2796"}, {"ruleId": "2044", "severity": 1, "message": "2797", "line": 586, "column": 6, "nodeType": "2046", "endLine": 586, "endColumn": 42, "suggestions": "2798"}, {"ruleId": "2044", "severity": 1, "message": "2799", "line": 600, "column": 6, "nodeType": "2046", "endLine": 600, "endColumn": 50, "suggestions": "2800"}, {"ruleId": "2044", "severity": 1, "message": "2801", "line": 877, "column": 5, "nodeType": "2046", "endLine": 877, "endColumn": 160, "suggestions": "2802"}, {"ruleId": "2044", "severity": 1, "message": "2803", "line": 945, "column": 5, "nodeType": "2046", "endLine": 945, "endColumn": 110, "suggestions": "2804"}, {"ruleId": "2044", "severity": 1, "message": "2805", "line": 975, "column": 5, "nodeType": "2046", "endLine": 975, "endColumn": 34, "suggestions": "2806"}, {"ruleId": "2044", "severity": 1, "message": "2807", "line": 993, "column": 5, "nodeType": "2046", "endLine": 993, "endColumn": 34, "suggestions": "2808"}, {"ruleId": "2044", "severity": 1, "message": "2807", "line": 1007, "column": 5, "nodeType": "2046", "endLine": 1007, "endColumn": 34, "suggestions": "2809"}, {"ruleId": "2044", "severity": 1, "message": "2807", "line": 1010, "column": 5, "nodeType": "2046", "endLine": 1010, "endColumn": 40, "suggestions": "2810"}, {"ruleId": "1940", "severity": 1, "message": "2811", "line": 1220, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 1220, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2812", "line": 1223, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 1223, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2333", "line": 2, "column": 64, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 71}, {"ruleId": "1940", "severity": 1, "message": "2598", "line": 2, "column": 73, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 79}, {"ruleId": "1940", "severity": 1, "message": "1991", "line": 15, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 15, "endColumn": 9}, {"ruleId": "1940", "severity": 1, "message": "2813", "line": 18, "column": 48, "nodeType": "1942", "messageId": "1943", "endLine": 18, "endColumn": 76}, {"ruleId": "1940", "severity": 1, "message": "2814", "line": 18, "column": 78, "nodeType": "1942", "messageId": "1943", "endLine": 18, "endColumn": 85}, {"ruleId": "1940", "severity": 1, "message": "2338", "line": 20, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 20, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2815", "line": 52, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 52, "endColumn": 31}, {"ruleId": "1940", "severity": 1, "message": "2816", "line": 54, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 54, "endColumn": 31}, {"ruleId": "1940", "severity": 1, "message": "2817", "line": 59, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 59, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2775", "line": 59, "column": 18, "nodeType": "1942", "messageId": "1943", "endLine": 59, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "2818", "line": 60, "column": 27, "nodeType": "1942", "messageId": "1943", "endLine": 60, "endColumn": 46}, {"ruleId": "1940", "severity": 1, "message": "2026", "line": 61, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 61, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2027", "line": 61, "column": 16, "nodeType": "1942", "messageId": "1943", "endLine": 61, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2752", "line": 85, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 85, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2626", "line": 92, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 92, "endColumn": 25}, {"ruleId": "2044", "severity": 1, "message": "2819", "line": 183, "column": 5, "nodeType": "2046", "endLine": 183, "endColumn": 52, "suggestions": "2820"}, {"ruleId": "2044", "severity": 1, "message": "2099", "line": 183, "column": 6, "nodeType": "2148", "endLine": 183, "endColumn": 51}, {"ruleId": "1940", "severity": 1, "message": "2247", "line": 2, "column": 92, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 102}, {"ruleId": "1940", "severity": 1, "message": "2821", "line": 76, "column": 19, "nodeType": "1942", "messageId": "1943", "endLine": 76, "endColumn": 30}, {"ruleId": "1940", "severity": 1, "message": "2506", "line": 2, "column": 27, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 34}, {"ruleId": "1940", "severity": 1, "message": "2332", "line": 2, "column": 36, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 46}, {"ruleId": "1940", "severity": 1, "message": "2331", "line": 2, "column": 59, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 67}, {"ruleId": "1940", "severity": 1, "message": "2333", "line": 2, "column": 77, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 84}, {"ruleId": "1940", "severity": 1, "message": "2822", "line": 3, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 3, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2823", "line": 4, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2824", "line": 8, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 8, "endColumn": 29}, {"ruleId": "1940", "severity": 1, "message": "2825", "line": 9, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2596", "line": 1, "column": 17, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2503", "line": 1, "column": 29, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 37}, {"ruleId": "1940", "severity": 1, "message": "2452", "line": 1, "column": 38, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 47}, {"ruleId": "1940", "severity": 1, "message": "2246", "line": 1, "column": 49, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 59}, {"ruleId": "1940", "severity": 1, "message": "2459", "line": 1, "column": 61, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 67}, {"ruleId": "1940", "severity": 1, "message": "2534", "line": 2, "column": 27, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 36}, {"ruleId": "1940", "severity": 1, "message": "2326", "line": 2, "column": 38, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 42}, {"ruleId": "1940", "severity": 1, "message": "2182", "line": 2, "column": 56, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 62}, {"ruleId": "1940", "severity": 1, "message": "1973", "line": 2, "column": 64, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 78}, {"ruleId": "1940", "severity": 1, "message": "2328", "line": 2, "column": 80, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 91}, {"ruleId": "1940", "severity": 1, "message": "2329", "line": 2, "column": 93, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 103}, {"ruleId": "1940", "severity": 1, "message": "2330", "line": 2, "column": 105, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 111}, {"ruleId": "1940", "severity": 1, "message": "2331", "line": 2, "column": 113, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 121}, {"ruleId": "1940", "severity": 1, "message": "2597", "line": 2, "column": 123, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 140}, {"ruleId": "1940", "severity": 1, "message": "2205", "line": 2, "column": 142, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 158}, {"ruleId": "1940", "severity": 1, "message": "2598", "line": 2, "column": 160, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 166}, {"ruleId": "1940", "severity": 1, "message": "2507", "line": 3, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 3, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2276", "line": 4, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2601", "line": 4, "column": 26, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 49}, {"ruleId": "1940", "severity": 1, "message": "2602", "line": 4, "column": 51, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 71}, {"ruleId": "1940", "severity": 1, "message": "2603", "line": 4, "column": 73, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 91}, {"ruleId": "1940", "severity": 1, "message": "2604", "line": 5, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 5, "endColumn": 31}, {"ruleId": "1940", "severity": 1, "message": "2605", "line": 7, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 7, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2606", "line": 8, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 8, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2607", "line": 9, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2608", "line": 10, "column": 7, "nodeType": "1942", "messageId": "1943", "endLine": 10, "endColumn": 12}, {"ruleId": "1940", "severity": 1, "message": "1971", "line": 11, "column": 7, "nodeType": "1942", "messageId": "1943", "endLine": 11, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2640", "line": 12, "column": 7, "nodeType": "1942", "messageId": "1943", "endLine": 12, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2826", "line": 19, "column": 7, "nodeType": "1942", "messageId": "1943", "endLine": 19, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2596", "line": 1, "column": 17, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2326", "line": 2, "column": 38, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 42}, {"ruleId": "1940", "severity": 1, "message": "1973", "line": 2, "column": 64, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 78}, {"ruleId": "1940", "severity": 1, "message": "2329", "line": 2, "column": 93, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 103}, {"ruleId": "1940", "severity": 1, "message": "2597", "line": 2, "column": 123, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 140}, {"ruleId": "1940", "severity": 1, "message": "2205", "line": 2, "column": 142, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 158}, {"ruleId": "1940", "severity": 1, "message": "2598", "line": 2, "column": 160, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 166}, {"ruleId": "1940", "severity": 1, "message": "2208", "line": 2, "column": 177, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 193}, {"ruleId": "1940", "severity": 1, "message": "2601", "line": 4, "column": 26, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 49}, {"ruleId": "1940", "severity": 1, "message": "2602", "line": 4, "column": 51, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 71}, {"ruleId": "1940", "severity": 1, "message": "2603", "line": 4, "column": 73, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 91}, {"ruleId": "1940", "severity": 1, "message": "2604", "line": 5, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 5, "endColumn": 31}, {"ruleId": "1940", "severity": 1, "message": "2605", "line": 7, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 7, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2606", "line": 8, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 8, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2607", "line": 9, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 11}, {"ruleId": "1940", "severity": 1, "message": "2608", "line": 10, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 10, "endColumn": 10}, {"ruleId": "1940", "severity": 1, "message": "1971", "line": 11, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 11, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2610", "line": 27, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 27, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2611", "line": 28, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 28, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2612", "line": 29, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 29, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2613", "line": 30, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 30, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2616", "line": 38, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 38, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2617", "line": 39, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 39, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2618", "line": 41, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 41, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2645", "line": 42, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 42, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2646", "line": 43, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 43, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2827", "line": 44, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 44, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2647", "line": 45, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 45, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2649", "line": 47, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 47, "endColumn": 30}, {"ruleId": "1940", "severity": 1, "message": "2650", "line": 48, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 48, "endColumn": 33}, {"ruleId": "1940", "severity": 1, "message": "2651", "line": 49, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 49, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2653", "line": 51, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 51, "endColumn": 30}, {"ruleId": "1940", "severity": 1, "message": "2654", "line": 52, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 52, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2655", "line": 53, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 53, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2656", "line": 54, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 54, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2521", "line": 58, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 58, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2828", "line": 152, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 152, "endColumn": 40}, {"ruleId": "1940", "severity": 1, "message": "2829", "line": 153, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 153, "endColumn": 41}, {"ruleId": "1940", "severity": 1, "message": "2830", "line": 154, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 154, "endColumn": 47}, {"ruleId": "1940", "severity": 1, "message": "2026", "line": 156, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 156, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2831", "line": 185, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 185, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2624", "line": 220, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 220, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2625", "line": 223, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 223, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2626", "line": 228, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 228, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2132", "line": 245, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 245, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2658", "line": 249, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 249, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2664", "line": 254, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 254, "endColumn": 20}, {"ruleId": "2044", "severity": 1, "message": "2832", "line": 263, "column": 6, "nodeType": "2046", "endLine": 263, "endColumn": 8, "suggestions": "2833"}, {"ruleId": "1940", "severity": 1, "message": "2834", "line": 298, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 298, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2596", "line": 1, "column": 17, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2459", "line": 1, "column": 49, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 55}, {"ruleId": "1940", "severity": 1, "message": "2835", "line": 1, "column": 69, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 80}, {"ruleId": "1940", "severity": 1, "message": "2326", "line": 2, "column": 38, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 42}, {"ruleId": "1940", "severity": 1, "message": "2329", "line": 2, "column": 93, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 103}, {"ruleId": "1940", "severity": 1, "message": "2597", "line": 2, "column": 123, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 140}, {"ruleId": "1940", "severity": 1, "message": "2205", "line": 2, "column": 142, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 158}, {"ruleId": "1940", "severity": 1, "message": "2598", "line": 2, "column": 160, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 166}, {"ruleId": "1940", "severity": 1, "message": "2460", "line": 2, "column": 195, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 212}, {"ruleId": "1940", "severity": 1, "message": "2601", "line": 5, "column": 26, "nodeType": "1942", "messageId": "1943", "endLine": 5, "endColumn": 49}, {"ruleId": "1940", "severity": 1, "message": "2602", "line": 5, "column": 51, "nodeType": "1942", "messageId": "1943", "endLine": 5, "endColumn": 71}, {"ruleId": "1940", "severity": 1, "message": "2603", "line": 5, "column": 73, "nodeType": "1942", "messageId": "1943", "endLine": 5, "endColumn": 91}, {"ruleId": "1940", "severity": 1, "message": "2604", "line": 6, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 6, "endColumn": 31}, {"ruleId": "1940", "severity": 1, "message": "2609", "line": 8, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 8, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "2610", "line": 9, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2611", "line": 10, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 10, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2612", "line": 11, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 11, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2613", "line": 12, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 12, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2614", "line": 13, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 13, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2605", "line": 15, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 15, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2606", "line": 16, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 16, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2607", "line": 17, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 17, "endColumn": 11}, {"ruleId": "1940", "severity": 1, "message": "2608", "line": 18, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 18, "endColumn": 10}, {"ruleId": "1940", "severity": 1, "message": "1971", "line": 19, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 19, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2823", "line": 34, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 34, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2836", "line": 44, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 44, "endColumn": 29}, {"ruleId": "1940", "severity": 1, "message": "2616", "line": 45, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 45, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2617", "line": 46, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 46, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2618", "line": 48, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 48, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2645", "line": 49, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 49, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2646", "line": 50, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 50, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2827", "line": 51, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 51, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "2647", "line": 52, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 52, "endColumn": 29}, {"ruleId": "1940", "severity": 1, "message": "2649", "line": 54, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 54, "endColumn": 35}, {"ruleId": "1940", "severity": 1, "message": "2650", "line": 55, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 55, "endColumn": 38}, {"ruleId": "1940", "severity": 1, "message": "2651", "line": 56, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 56, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "2653", "line": 58, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 58, "endColumn": 35}, {"ruleId": "1940", "severity": 1, "message": "2654", "line": 59, "column": 6, "nodeType": "1942", "messageId": "1943", "endLine": 59, "endColumn": 29}, {"ruleId": "1940", "severity": 1, "message": "2655", "line": 60, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 60, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2656", "line": 61, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 61, "endColumn": 29}, {"ruleId": "1940", "severity": 1, "message": "2837", "line": 63, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 63, "endColumn": 29}, {"ruleId": "1940", "severity": 1, "message": "2521", "line": 66, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 66, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2828", "line": 89, "column": 11, "nodeType": "1942", "messageId": "1943", "endLine": 89, "endColumn": 42}, {"ruleId": "1940", "severity": 1, "message": "2829", "line": 90, "column": 11, "nodeType": "1942", "messageId": "1943", "endLine": 90, "endColumn": 43}, {"ruleId": "1940", "severity": 1, "message": "2830", "line": 91, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 91, "endColumn": 46}, {"ruleId": "2044", "severity": 1, "message": "2838", "line": 112, "column": 5, "nodeType": "2046", "endLine": 112, "endColumn": 52, "suggestions": "2839"}, {"ruleId": "1940", "severity": 1, "message": "2624", "line": 117, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 117, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2625", "line": 120, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 120, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2626", "line": 125, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 125, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2840", "line": 135, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 135, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2132", "line": 175, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 175, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2841", "line": 183, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 183, "endColumn": 20}, {"ruleId": "2044", "severity": 1, "message": "2832", "line": 188, "column": 4, "nodeType": "2046", "endLine": 188, "endColumn": 6, "suggestions": "2842"}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 193, "column": 19, "nodeType": "2095", "messageId": "2096", "endLine": 193, "endColumn": 21}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 194, "column": 19, "nodeType": "2095", "messageId": "2096", "endLine": 194, "endColumn": 21}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 194, "column": 39, "nodeType": "2095", "messageId": "2096", "endLine": 194, "endColumn": 41}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 213, "column": 19, "nodeType": "2095", "messageId": "2096", "endLine": 213, "endColumn": 21}, {"ruleId": "2093", "severity": 1, "message": "2115", "line": 226, "column": 20, "nodeType": "2095", "messageId": "2096", "endLine": 226, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2843", "line": 279, "column": 7, "nodeType": "1942", "messageId": "1943", "endLine": 279, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2026", "line": 307, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 307, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2831", "line": 334, "column": 7, "nodeType": "1942", "messageId": "1943", "endLine": 334, "endColumn": 23}, {"ruleId": "2044", "severity": 1, "message": "2844", "line": 371, "column": 4, "nodeType": "2046", "endLine": 371, "endColumn": 6, "suggestions": "2845"}, {"ruleId": "1940", "severity": 1, "message": "2245", "line": 2, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2534", "line": 2, "column": 27, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 36}, {"ruleId": "1940", "severity": 1, "message": "2326", "line": 2, "column": 38, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 42}, {"ruleId": "1940", "severity": 1, "message": "2348", "line": 9, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2347", "line": 9, "column": 22, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 36}, {"ruleId": "1940", "severity": 1, "message": "2050", "line": 10, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 10, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2349", "line": 10, "column": 26, "nodeType": "1942", "messageId": "1943", "endLine": 10, "endColumn": 44}, {"ruleId": "1940", "severity": 1, "message": "2738", "line": 12, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 12, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2818", "line": 12, "column": 27, "nodeType": "1942", "messageId": "1943", "endLine": 12, "endColumn": 46}, {"ruleId": "1940", "severity": 1, "message": "2739", "line": 13, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 13, "endColumn": 12}, {"ruleId": "1940", "severity": 1, "message": "2740", "line": 13, "column": 14, "nodeType": "1942", "messageId": "1943", "endLine": 13, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2432", "line": 15, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 15, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2674", "line": 16, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 16, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2846", "line": 28, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 28, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2847", "line": 3, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 3, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2609", "line": 6, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 6, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "2247", "line": 2, "column": 32, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 42}, {"ruleId": "1940", "severity": 1, "message": "2534", "line": 2, "column": 44, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 53}, {"ruleId": "1940", "severity": 1, "message": "2544", "line": 4, "column": 46, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 65}, {"ruleId": "1940", "severity": 1, "message": "1971", "line": 4, "column": 67, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 75}, {"ruleId": "1940", "severity": 1, "message": "2848", "line": 8, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 8, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2769", "line": 16, "column": 13, "nodeType": "1942", "messageId": "1943", "endLine": 16, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2849", "line": 31, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 31, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2362", "line": 33, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 33, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2850", "line": 43, "column": 6, "nodeType": "1942", "messageId": "1943", "endLine": 43, "endColumn": 34}, {"ruleId": "1940", "severity": 1, "message": "2851", "line": 85, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 85, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "2565", "line": 95, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 95, "endColumn": 26}, {"ruleId": "1940", "severity": 1, "message": "2824", "line": 5, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 5, "endColumn": 29}, {"ruleId": "1940", "severity": 1, "message": "2825", "line": 6, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 6, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2852", "line": 7, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 7, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2853", "line": 27, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 27, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2854", "line": 34, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 34, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2530", "line": 56, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 56, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2531", "line": 58, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 58, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2855", "line": 80, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 80, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2856", "line": 82, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 82, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2857", "line": 82, "column": 23, "nodeType": "1942", "messageId": "1943", "endLine": 82, "endColumn": 38}, {"ruleId": "1940", "severity": 1, "message": "2858", "line": 133, "column": 7, "nodeType": "1942", "messageId": "1943", "endLine": 133, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2859", "line": 290, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 290, "endColumn": 28}, {"ruleId": "2044", "severity": 1, "message": "2860", "line": 344, "column": 5, "nodeType": "2046", "endLine": 344, "endColumn": 22, "suggestions": "2861"}, {"ruleId": "1940", "severity": 1, "message": "2760", "line": 1, "column": 58, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 67}, {"ruleId": "1940", "severity": 1, "message": "2506", "line": 2, "column": 15, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2247", "line": 2, "column": 24, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 34}, {"ruleId": "1940", "severity": 1, "message": "2333", "line": 2, "column": 48, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 55}, {"ruleId": "1940", "severity": 1, "message": "2768", "line": 5, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 5, "endColumn": 11}, {"ruleId": "1940", "severity": 1, "message": "2202", "line": 6, "column": 41, "nodeType": "1942", "messageId": "1943", "endLine": 6, "endColumn": 53}, {"ruleId": "1940", "severity": 1, "message": "2766", "line": 7, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 7, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2211", "line": 7, "column": 16, "nodeType": "1942", "messageId": "1943", "endLine": 7, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2862", "line": 7, "column": 24, "nodeType": "1942", "messageId": "1943", "endLine": 7, "endColumn": 29}, {"ruleId": "1940", "severity": 1, "message": "2863", "line": 7, "column": 31, "nodeType": "1942", "messageId": "1943", "endLine": 7, "endColumn": 35}, {"ruleId": "1940", "severity": 1, "message": "2384", "line": 7, "column": 37, "nodeType": "1942", "messageId": "1943", "endLine": 7, "endColumn": 47}, {"ruleId": "1940", "severity": 1, "message": "2767", "line": 7, "column": 49, "nodeType": "1942", "messageId": "1943", "endLine": 7, "endColumn": 61}, {"ruleId": "1940", "severity": 1, "message": "2507", "line": 8, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 8, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2823", "line": 9, "column": 8, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2640", "line": 10, "column": 20, "nodeType": "1942", "messageId": "1943", "endLine": 10, "endColumn": 30}, {"ruleId": "1940", "severity": 1, "message": "2864", "line": 46, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 46, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2865", "line": 47, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 47, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2057", "line": 48, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 48, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2866", "line": 49, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 49, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2391", "line": 50, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 50, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2867", "line": 51, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 51, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2390", "line": 52, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 52, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2389", "line": 53, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 53, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2868", "line": 54, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 54, "endColumn": 12}, {"ruleId": "1940", "severity": 1, "message": "2387", "line": 56, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 56, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2869", "line": 57, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 57, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2469", "line": 61, "column": 4, "nodeType": "1942", "messageId": "1943", "endLine": 61, "endColumn": 11}, {"ruleId": "1940", "severity": 1, "message": "2536", "line": 75, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 75, "endColumn": 22}, {"ruleId": "2044", "severity": 1, "message": "2870", "line": 171, "column": 7, "nodeType": "2046", "endLine": 171, "endColumn": 27, "suggestions": "2871"}, {"ruleId": "2044", "severity": 1, "message": "2872", "line": 193, "column": 9, "nodeType": "2303", "endLine": 198, "endColumn": 4, "suggestions": "2873"}, {"ruleId": "2044", "severity": 1, "message": "2874", "line": 275, "column": 6, "nodeType": "2046", "endLine": 275, "endColumn": 19, "suggestions": "2875"}, {"ruleId": "2044", "severity": 1, "message": "2876", "line": 349, "column": 6, "nodeType": "2046", "endLine": 349, "endColumn": 17, "suggestions": "2877"}, {"ruleId": "1940", "severity": 1, "message": "2207", "line": 2, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2878", "line": 20, "column": 24, "nodeType": "1942", "messageId": "1943", "endLine": 20, "endColumn": 35}, {"ruleId": "1940", "severity": 1, "message": "2359", "line": 42, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 42, "endColumn": 21}, {"ruleId": "1940", "severity": 1, "message": "2464", "line": 43, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 43, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2223", "line": 44, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 44, "endColumn": 28}, {"ruleId": "1940", "severity": 1, "message": "2879", "line": 46, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 46, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2319", "line": 48, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 48, "endColumn": 17}, {"ruleId": "1940", "severity": 1, "message": "2362", "line": 49, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 49, "endColumn": 20}, {"ruleId": "1940", "severity": 1, "message": "2343", "line": 61, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 61, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2344", "line": 61, "column": 45, "nodeType": "1942", "messageId": "1943", "endLine": 61, "endColumn": 62}, {"ruleId": "1940", "severity": 1, "message": "2880", "line": 101, "column": 11, "nodeType": "1942", "messageId": "1943", "endLine": 101, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2881", "line": 104, "column": 12, "nodeType": "1942", "messageId": "1943", "endLine": 104, "endColumn": 26}, {"ruleId": "2044", "severity": 1, "message": "2570", "line": 129, "column": 5, "nodeType": "2046", "endLine": 129, "endColumn": 155, "suggestions": "2882"}, {"ruleId": "1940", "severity": 1, "message": "2514", "line": 1, "column": 27, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 41}, {"ruleId": "1940", "severity": 1, "message": "2883", "line": 22, "column": 7, "nodeType": "1942", "messageId": "1943", "endLine": 22, "endColumn": 23}, {"ruleId": "1940", "severity": 1, "message": "2884", "line": 31, "column": 7, "nodeType": "1942", "messageId": "1943", "endLine": 31, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2885", "line": 40, "column": 7, "nodeType": "1942", "messageId": "1943", "endLine": 40, "endColumn": 25}, {"ruleId": "1940", "severity": 1, "message": "2886", "line": 5, "column": 3, "nodeType": "1942", "messageId": "1943", "endLine": 5, "endColumn": 24}, {"ruleId": "2044", "severity": 1, "message": "2887", "line": 121, "column": 6, "nodeType": "2046", "endLine": 121, "endColumn": 26, "suggestions": "2888"}, {"ruleId": "1940", "severity": 1, "message": "2889", "line": 39, "column": 9, "nodeType": "1942", "messageId": "1943", "endLine": 39, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2246", "line": 1, "column": 17, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 27}, {"ruleId": "1940", "severity": 1, "message": "2503", "line": 1, "column": 28, "nodeType": "1942", "messageId": "1943", "endLine": 1, "endColumn": 36}, {"ruleId": "1940", "severity": 1, "message": "1951", "line": 2, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 2, "endColumn": 22}, {"ruleId": "1940", "severity": 1, "message": "2250", "line": 4, "column": 10, "nodeType": "1942", "messageId": "1943", "endLine": 4, "endColumn": 24}, {"ruleId": "1940", "severity": 1, "message": "2721", "line": 6, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 6, "endColumn": 11}, {"ruleId": "1940", "severity": 1, "message": "2722", "line": 7, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 7, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "1973", "line": 8, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 8, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2890", "line": 9, "column": 2, "nodeType": "1942", "messageId": "1943", "endLine": 9, "endColumn": 19}, {"ruleId": "1940", "severity": 1, "message": "2245", "line": 11, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 11, "endColumn": 8}, {"ruleId": "1940", "severity": 1, "message": "2327", "line": 13, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 13, "endColumn": 14}, {"ruleId": "1940", "severity": 1, "message": "2209", "line": 15, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 15, "endColumn": 16}, {"ruleId": "1940", "severity": 1, "message": "2210", "line": 16, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 16, "endColumn": 18}, {"ruleId": "1940", "severity": 1, "message": "2329", "line": 18, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 18, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2330", "line": 19, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 19, "endColumn": 11}, {"ruleId": "1940", "severity": 1, "message": "2331", "line": 20, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 20, "endColumn": 13}, {"ruleId": "1940", "severity": 1, "message": "2332", "line": 21, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 21, "endColumn": 15}, {"ruleId": "1940", "severity": 1, "message": "2333", "line": 22, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 22, "endColumn": 12}, {"ruleId": "1940", "severity": 1, "message": "2334", "line": 23, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 23, "endColumn": 10}, {"ruleId": "1940", "severity": 1, "message": "2335", "line": 24, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 24, "endColumn": 9}, {"ruleId": "1940", "severity": 1, "message": "2891", "line": 25, "column": 5, "nodeType": "1942", "messageId": "1943", "endLine": 25, "endColumn": 21}, "@typescript-eslint/no-unused-vars", "'GuidePopup' is defined but never used.", "Identifier", "unusedVar", "'Rte' is defined but never used.", "'isAppReady' is assigned a value but never used.", "'LoginUserInfo' is defined but never used.", "'useMemo' is defined but never used.", "'Steps' is defined but never used.", "'PopupList' is defined but never used.", "'BUTTON_DEFAULT_VALUE' is defined but never used.", "'stopScraping' is defined but never used.", "'addicon' is defined but never used.", "'touricon' is defined but never used.", "'ProductToursicon' is defined but never used.", "'Tooltipsicon' is defined but never used.", "'announcementicon' is defined but never used.", "'Bannersicon' is defined but never used.", "'Checklisticon' is defined but never used.", "'Hotspoticon' is defined but never used.", "'Surveyicon' is defined but never used.", "'Announcementsicon' is defined but never used.", "'bannersicon' is defined but never used.", "'tooltipicon' is defined but never used.", "'checklisticon' is defined but never used.", "'hotspotsicon' is defined but never used.", "'surveysicon' is defined but never used.", "'settingsicon' is defined but never used.", "'undoicon' is defined but never used.", "'redoicon' is defined but never used.", "'shareicon' is defined but never used.", "'editicon' is defined but never used.", "'Outlet' is defined but never used.", "'InputAdornment' is defined but never used.", "'FormHelperText' is defined but never used.", "'List' is defined but never used.", "'Step' is defined but never used.", "'guideSetting' is defined but never used.", "'JSEncrypt' is defined but never used.", "'GetUserDetailsById' is defined but never used.", "'UserLogin' is defined but never used.", "'VisibilityOff' is defined but never used.", "'Visibility' is defined but never used.", "'initialsData' is defined but never used.", "'EditIcon' is defined but never used.", "'TooltipUserview' is defined but never used.", "'SubmitUpdateGuid' is defined but never used.", "'PageInteractions' is defined but never used.", "'ElementsSettings' is defined but never used.", "'DrawerState' is defined but never used.", "'Checklist' is defined but never used.", "'Padding' is defined but never used.", "'CheckIcon' is defined but never used.", "'TooltipPreview' is defined but never used.", "'DismissData' is defined but never used.", "'Canvas' is defined but never used.", "'Design' is defined but never used.", "'Advanced' is defined but never used.", "'Hotspot' is defined but never used.", "'stepId' is defined but never used.", "'userId' is defined but never used.", "'loginUserData' is defined but never used.", "'setIsGuidesListOpen' is assigned a value but never used.", "'setIsInHomeScreen' is assigned a value but never used.", "'setIsAnnouncementListOpen' is assigned a value but never used.", "'setIsBannerslistOpen' is assigned a value but never used.", "'selectedTemplated' is assigned a value but never used.", "'setSelectedTemplated' is assigned a value but never used.", "'errorInStepName' is assigned a value but never used.", "'showTextField' is assigned a value but never used.", "'signOut' is assigned a value but never used.", "'selectedElement' is assigned a value but never used.", "'setSelectedElement' is assigned a value but never used.", "'showPassword' is assigned a value but never used.", "'setShowPassword' is assigned a value but never used.", "'password' is assigned a value but never used.", "'setPassword' is assigned a value but never used.", "'loginUserInfo' is assigned a value but never used.", "'setLoginUserInfo' is assigned a value but never used.", "'setresponse' is assigned a value but never used.", "'isTooltipPopupOpen' is assigned a value but never used.", "'setIsTooltipPopupOpen' is assigned a value but never used.", "'email' is assigned a value but never used.", "'setEmail' is assigned a value but never used.", "'loginUserDetails' is assigned a value but never used.", "'setUserDetails' is assigned a value but never used.", "'error' is assigned a value but never used.", "'setError' is assigned a value but never used.", "'isSelectingElement' is assigned a value but never used.", "'selectedElementDetails' is assigned a value but never used.", "'setSelectedElementDetails' is assigned a value but never used.", "'position' is assigned a value but never used.", "'setPosition' is assigned a value but never used.", "'radius' is assigned a value but never used.", "'setRadius' is assigned a value but never used.", "'borderSize' is assigned a value but never used.", "'setBorderSize' is assigned a value but never used.", "'announcementData' is assigned a value but never used.", "'currentUrl' is assigned a value but never used.", "'isBannerPopupOpen' is assigned a value but never used.", "'i18nInitialized' is assigned a value but never used.", "'setI18nInitialized' is assigned a value but never used.", "'hashValue' is assigned a value but never used.", "'setHashValue' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has an unnecessary dependency: 'window.location.href'. Either exclude it or remove the dependency array. Outer scope values like 'window.location.href' aren't valid dependencies because mutating them doesn't re-render the component.", "ArrayExpression", ["2892"], "'fit' is assigned a value but never used.", "'fill' is assigned a value but never used.", "'backgroundColor' is assigned a value but never used.", "'sectionHeight' is assigned a value but never used.", "'setSectionHeight' is assigned a value but never used.", "'guidedatas' is assigned a value but never used.", "'setGuideDataS' is assigned a value but never used.", "'hotspotPopup' is assigned a value but never used.", "'setHotspotPopup' is assigned a value but never used.", "'textvaluess' is assigned a value but never used.", "'preview' is assigned a value but never used.", "'btnBorderColor' is assigned a value but never used.", "'btnBgColor' is assigned a value but never used.", "'btnTextColor' is assigned a value but never used.", "'isTooltipPopup' is assigned a value but never used.", "'setSteps' is assigned a value but never used.", "'newCurrentStep' is assigned a value but never used.", "'updateCanvasInTooltip' is assigned a value but never used.", "'hotspbgcolor' is assigned a value but never used.", "'setHotspBgColor' is assigned a value but never used.", "'setHotspotDataOnEdit' is assigned a value but never used.", "'openTooltip' is assigned a value but never used.", "'setXpathToTooltipMetaData' is assigned a value but never used.", "'setAxisData' is assigned a value but never used.", "'axisData' is assigned a value but never used.", "'setAutoPosition' is assigned a value but never used.", "'targetURL' is assigned a value but never used.", "'elementButtonName' is assigned a value but never used.", "'setElementButtonName' is assigned a value but never used.", "'isSaveClicked' is assigned a value but never used.", "'setbtnidss' is assigned a value but never used.", "'setPulseAnimationsH' is assigned a value but never used.", "'tooltipCount' is assigned a value but never used.", "'HotspotGuideDetails' is assigned a value but never used.", "'TooltipGuideDetailsNew' is assigned a value but never used.", "'editClicked' is assigned a value but never used.", "'textArray' is assigned a value but never used.", "'setTextArray' is assigned a value but never used.", "'setDrawerActiveMenu' is assigned a value but never used.", "'setDrawerSearchText' is assigned a value but never used.", "'setInteractionData' is assigned a value but never used.", "'syncAIAnnouncementCanvasSettings' is assigned a value but never used.", "'ele4' is assigned a value but never used.", "'targetElement' is assigned a value but never used.", "'setHotspotClicked' is assigned a value but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "React Hook useEffect has missing dependencies: 'fetchGuideDetails' and 'hotspot'. Either include them or remove the dependency array.", ["2893"], "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "MemberExpression", "React Hook useEffect has a missing dependency: 'setDesignPopup'. Either include it or remove the dependency array.", ["2894"], "'screenWidth' is assigned a value but never used.", "'dialogWidth' is assigned a value but never used.", "'handlechangeStep' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'determineCurrentScreen'. Either include it or remove the dependency array.", ["2895"], "React Hook useEffect has a missing dependency: 'errors'. Either include it or remove the dependency array. Outer scope values like 'selectedStepType' aren't valid dependencies because mutating them doesn't re-render the component.", ["2896"], "React Hook useEffect has a missing dependency: 'setIsAIGuidePersisted'. Either include it or remove the dependency array.", ["2897"], "React Hook useEffect has missing dependencies: 'bannerPopup', 'clearBannerButtonDetials', 'currentGuideId', 'currentStep', 'selectedTemplate', 'selectedTemplateTour', 'setBannerButtonSelected', 'setBannerPopup', 'setIsAnnouncementCreationBuilderOpen', 'setIsBannerCreationBuilderOpen', 'setIsHotspotCreationBuilderOpen', 'setIsTooltipCreationBuilderOpen', 'updateButtonContainerOnReload', and 'updateRTEContainerOnReload'. Either include them or remove the dependency array.", ["2898"], "'setCount' is assigned a value but never used.", "Expected '===' and instead saw '=='.", "'handleGuidesSettingsclick' is assigned a value but never used.", "'synchronizePreviewData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'elementSelected', 'resetALTKeywordForNewTooltip', 'setElementSelected', 'setIsALTKeywordEnabled', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["2899"], "'handleElementSelectionToggle' is assigned a value but never used.", "'isAnnouncementOpen' is assigned a value but never used.", "'setAnnouncementOpen' is assigned a value but never used.", "'aiCreationComplete' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setSettingAnchorEl'. Either include it or remove the dependency array.", ["2900"], "'defaultButtonSection' is assigned a value but never used.", "'responseData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'setIsUnSavedChanges' and 'stepCreation'. Either include them or remove the dependency array.", ["2901"], "'handleNewInteractionClick' is assigned a value but never used.", "Assignments to the 'accountId' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "'handleEditClick' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'userType'. Either include it or remove the dependency array.", ["2902"], "'editstepNameClicked' is assigned a value but never used.", "'setEditStepNameClicked' is assigned a value but never used.", "'handleNextClick' is assigned a value but never used.", "'isValid' is assigned a value but never used.", "'handleEventChange' is assigned a value but never used.", "'isGuideNameUnique' is assigned a value but never used.", "React Hook useEffect has an unnecessary dependency: 'updatedGuideData.GuideStep'. Either exclude it or remove the dependency array. Outer scope values like 'updatedGuideData.GuideStep' aren't valid dependencies because mutating them doesn't re-render the component.", ["2903"], "'getAlignment' is defined but never used.", "'popupVisible' is assigned a value but never used.", "'triggerType' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'currentGuide?.GuideStep'. Either include it or remove the dependency array.", ["2904"], "ChainExpression", "'customButton' is assigned a value but never used.", "'groupedButtons' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'cleanupDuplicateSteps', 'createWithAI', 'currentGuideId', 'interactionData', and 'resetHeightofBanner'. Either include them or remove the dependency array.", ["2905"], "'isDisabled' is assigned a value but never used.", "'guideType' is assigned a value but never used.", "'guideSteps' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'SetGuideName', 'cleanupDuplicateSteps', 'createWithAI', 'currentStep', 'getGuideTypeValue', 'handlecreatefromScratchclick', 'overlayEnabled', 'pageinteraction', 'progress', 'resetHeightofBanner', 'setBannerPopup', 'setBposition', 'setDismiss', 'setIsGuideInfoScreen', 'setOverlayEnabled', 'setPageInteraction', 'setProgress', 'setProgressColor', 'setSelectedOption', 'setSelectedTemplate', 'setSelectedTemplateTour', 'setTooltipCount', and 'setTourDataOnEdit'. Either include them or remove the dependency array.", ["2906"], "React Hook useEffect has missing dependencies: 'SetGuideName', 'currentGuideId', 'currentStep', 'getGuideTypeValue', 'handlecreatefromScratchclick', 'selectedTemplate', 'setIsGuideInfoScreen', 'setSelectedTemplate', and 'steps'. Either include them or remove the dependency array.", ["2907"], "React Hook useEffect has missing dependencies: 'setBannerPopup', 'setCreateWithAI', 'setCurrentGuideId', 'setIsAIGuidePersisted', 'setIsAnnouncementCreationBuilderOpen', 'setIsBannerCreationBuilderOpen', 'setIsGuideInfoScreen', 'setIsHomeScreen', 'setIsHotspotCreationBuilderOpen', 'setIsTemplateScreen', 'setIsTooltipCreationBuilderOpen', and 'setIsTooltipPopup'. Either include them or remove the dependency array.", ["2908"], "React Hook useEffect has an unnecessary dependency: 'updatedGuideData'. Either exclude it or remove the dependency array. Outer scope values like 'updatedGuideData' aren't valid dependencies because mutating them doesn't re-render the component.", ["2909"], "'getAccountIdForUpdate' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'deleteClicked', 'handleStepChange', and 'updateStepClicked'. Either include them or remove the dependency array.", ["2910"], "no-useless-escape", "Unnecessary escape character: \\/.", "Literal", "unnecessaryEscape", ["2911", "2912"], "'selectedStepTitle' is assigned a value but never used.", "'UserManager' is defined but never used.", "'useNavigate' is defined but never used.", "'useLocation' is defined but never used.", "'redirectPath' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loggedOut'. Either include it or remove the dependency array.", ["2913"], "'signIn' is assigned a value but never used.", "'CelebrationOutlinedIcon' is defined but never used.", "'ErrorOutlineOutlinedIcon' is defined but never used.", "'Button' is defined but never used.", "'Routes' is defined but never used.", "'RouteSharp' is defined but never used.", "'extractStateForHistory' is defined but never used.", "no-dupe-keys", "Duplicate key 'hotspotXaxis'.", "ObjectExpression", "Duplicate key 'setHotspotXaxis'.", "Duplicate key 'setSelectedTemplate'.", "no-self-assign", "'state.overlayEnabled' is assigned to itself.", "selfAssignment", "Duplicate key 'toolTipGuideMetaData'.", "'isTourBanner' is assigned a value but never used.", "'announcementGuideStep' is assigned a value but never used.", "Duplicate key 'announcementGuideMetaData'.", "'opt' is assigned a value but never used.", "'targetStep' is assigned a value but never used.", "'future' is assigned a value but never used.", "'FALSE' is defined but never used.", "'TSectionType' is defined but never used.", "'RadioGroup' is defined but never used.", "'Radio' is defined but never used.", "'FormControlLabel' is defined but never used.", "'Input' is defined but never used.", "'Autocomplete' is defined but never used.", "'CircularProgress' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogActions' is defined but never used.", "'GifBox' is defined but never used.", "'Opacity' is defined but never used.", "'WarningIcon' is defined but never used.", "'color' is defined but never used.", "'dismissData' is assigned a value but never used.", "'setSelectActions' is assigned a value but never used.", "'setSelectedInteraction' is assigned a value but never used.", "'openInteractionList' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'currentStepIndex' is assigned a value but never used.", "'setProgress' is assigned a value but never used.", "'selectedTemplate' is assigned a value but never used.", "'updateTooltipButtonAction' is assigned a value but never used.", "'updateTooltipButtonInteraction' is assigned a value but never used.", "'selectedTemplateTour' is assigned a value but never used.", "'setProgressColor' is assigned a value but never used.", "'createWithAI' is assigned a value but never used.", "'action' is assigned a value but never used.", "'designPopup' is assigned a value but never used.", "'buttonId' is assigned a value but never used.", "'setButtonId' is assigned a value but never used.", "'cuntainerId' is assigned a value but never used.", "'setCuntainerId' is assigned a value but never used.", "'btnname' is assigned a value but never used.", "'handleCloseInteraction' is assigned a value but never used.", "'handleOpenInteraction' is assigned a value but never used.", "'sideAddButtonStyle' is assigned a value but never used.", "React Hook useMemo has missing dependencies: 'getCurrentButtonInfo', 'setBtnName', and 'setCurrentButtonName'. Either include them or remove the dependency array.", ["2914"], "'selectedButton' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'defaultButtonColors.backgroundColor', 'defaultButtonColors.borderColor', 'defaultButtonColors.color', 'getCurrentButtonInfo', 'setTargetURL', and 'targetURL'. Either include them or remove the dependency array.", ["2915"], "React Hook useEffect has missing dependencies: 'selectedActions.value' and 'targetURL'. Either include them or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setSelectedActions' needs the current value of 'selectedActions.value'.", ["2916"], "'Box' is defined but never used.", "'useContext' is defined but never used.", "'Typography' is defined but never used.", "'AuthProvider' is defined but never used.", "'useAuth' is defined but never used.", "'AccountContext' is defined but never used.", "'clearAccessToken' is assigned a value but never used.", "'userLocalData' is assigned a value but never used.", "'SAinitialsData' is assigned a value but never used.", "'userDetails' is defined but never used.", "'ai' is defined but never used.", "'EnableAIButton' is defined but never used.", "'IsOpenAIKeyEnabledForAccount' is defined but never used.", "'setSelectedTemplate' is assigned a value but never used.", "'setSelectedTemplateTour' is assigned a value but never used.", "'steps' is assigned a value but never used.", "'setTooltipCount' is assigned a value but never used.", "'SetGuideName' is assigned a value but never used.", "'setIsTooltipPopup' is assigned a value but never used.", "'setBannerPopup' is assigned a value but never used.", "'setElementSelected' is assigned a value but never used.", "'TooltipGuideDetails' is assigned a value but never used.", "'HotspotGuideDetailsNew' is assigned a value but never used.", "'setSelectedStepTypeHotspot' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'isExtensionClosed' and 'setIsExtensionClosed'. Either include them or remove the dependency array.", ["2917"], "React Hook useEffect has missing dependencies: 'setDrawerActiveMenu', 'setDrawerSearchText', and 'setHasAnnouncementOpened'. Either include them or remove the dependency array.", ["2918"], "React Hook useEffect has missing dependencies: 'setDrawerActiveMenu', 'setDrawerSearchText', and 'setIsPopupOpen'. Either include them or remove the dependency array. If 'setIsPopupOpen' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2919"], "'handleEnableAI' is assigned a value but never used.", "'useDrawerStore' is defined but never used.", "'constants' is defined but never used.", "'addPersistentHighlight' is assigned a value but never used.", "'showClickFeedback' is assigned a value but never used.", ["2920", "2921"], "Unnecessary escape character: \\..", ["2922", "2923"], ["2924", "2925"], ["2926", "2927"], ["2928", "2929"], ["2930", "2931"], ["2932", "2933"], ["2934", "2935"], "'response' is assigned a value but never used.", "'errorMessage' is assigned a value but never used.", "'axios' is defined but never used.", "'AnyMxRecord' is defined but never used.", "'ChecklistPopup' is defined but never used.", "'closeicon' is defined but never used.", "'closepluginicon' is defined but never used.", "'setShowLauncherSettings' is assigned a value but never used.", "'showLauncherSettings' is assigned a value but never used.", "'setIcons' is assigned a value but never used.", "'checklistColor' is assigned a value but never used.", "'GetGudeDetailsByGuideId' is defined but never used.", "'initialCompletedStatus' is assigned a value but never used.", "The 'checkpointslistData' logical expression could make the dependencies of useEffect Hook (at line 143) change on every render. To fix this, wrap the initialization of 'checkpointslistData' in its own useMemo() Hook.", "VariableDeclarator", "The 'checkpointslistData' logical expression could make the dependencies of useEffect Hook (at line 207) change on every render. To fix this, wrap the initialization of 'checkpointslistData' in its own useMemo() Hook.", "'checklistItems' is assigned a value but never used.", "'setChecklistItems' is assigned a value but never used.", "no-lone-blocks", "Nested block is redundant.", "BlockStatement", "redundantNestedBlock", "React Hook useEffect has a missing dependency: 'checkpointslistData'. Either include it or remove the dependency array.", ["2936"], "'iconColor' is assigned a value but never used.", "'base64IconFinal' is assigned a value but never used.", "'handleNavigate' is assigned a value but never used.", "'anchorEl' is assigned a value but never used.", "'setAnchorEl' is assigned a value but never used.", "'currentStep' is assigned a value but never used.", "'setCurrentStep' is assigned a value but never used.", "'scrollPercentage' is assigned a value but never used.", "'setScrollPercentage' is assigned a value but never used.", "'UndoIcon' is defined but never used.", "'RedoIcon' is defined but never used.", "'canUndoValue' is assigned a value but never used.", "'canRedoValue' is assigned a value but never used.", "'Grid' is defined but never used.", "'Container' is defined but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'IconButton' is defined but never used.", "'Tooltip' is defined but never used.", "'Alert' is defined but never used.", "'Chip' is defined but never used.", "'ViewModuleIcon' is defined but never used.", "'CodeIcon' is defined but never used.", "'TouchAppSharp' is defined but never used.", "'reselectElement' is assigned a value but never used.", "'setReselectElement' is assigned a value but never used.", "'goToNextElement' is assigned a value but never used.", "'setGoToNextElement' is assigned a value but never used.", "'setCurrentGuideId' is assigned a value but never used.", "'getCurrentGuideId' is assigned a value but never used.", "'padding' is assigned a value but never used.", "'setPadding' is assigned a value but never used.", "'setBorderColor' is assigned a value but never used.", "'borderColor' is assigned a value but never used.", "'setBackgroundColor' is assigned a value but never used.", "'overlayEnabled' is assigned a value but never used.", "'setZiindex' is assigned a value but never used.", "'setguidesSettingspopup' is assigned a value but never used.", "'setTooltipBackgroundcolor' is assigned a value but never used.", "'setTooltipBordercolor' is assigned a value but never used.", "'setTooltipBordersize' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE' is assigned a value but never used.", "'savedGuideData' is assigned a value but never used.", "'ButtonsDropdown' is assigned a value but never used.", "'setButtonsDropdown' is assigned a value but never used.", "'elementSelected' is assigned a value but never used.", "'elementbuttonClick' is assigned a value but never used.", "'highlightedButton' is assigned a value but never used.", "'mapButtonSection' is assigned a value but never used.", "'progress' is assigned a value but never used.", "'setSelectedOption' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'setHotspotPopup', 'setShowLauncherSettings', 'setShowTooltipCanvasSettings', and 'setTitlePopup'. Either include them or remove the dependency array.", ["2937"], "'toggleReselectElement' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'SetElementButtonClick', 'createWithAI', 'currentGuideId', 'interactionData', 'setButtonClick', 'setDropdownValue', 'setElementButtonName', 'setElementClick', and 'setbtnidss'. Either include them or remove the dependency array.", ["2938"], "'existingHotspot' is assigned a value but never used.", "'existingTooltip' is assigned a value but never used.", "'toggleCustomCSS' is assigned a value but never used.", "'toggleAnimation' is assigned a value but never used.", "'handleDismissDataChange' is assigned a value but never used.", "'setTooltipXaxis' is defined but never used.", "'setTooltipYaxis' is defined but never used.", "'setTooltipPosition' is defined but never used.", "'setTooltipBorderradius' is defined but never used.", "'setTooltipPadding' is defined but never used.", "'setTooltipWidth' is defined but never used.", "'updateCanvasInTooltip' is defined but never used.", "'setElementSelected' is defined but never used.", "'TextFormat' is defined but never used.", "'BUTTON_CONT_DEF_VALUE' is defined but never used.", "'saveGuide' is defined but never used.", "'setSectionColor' is assigned a value but never used.", "'setButtonProperty' is assigned a value but never used.", "'BborderSize' is assigned a value but never used.", "'Bbordercolor' is assigned a value but never used.", "'backgroundC' is assigned a value but never used.", "'setPreview' is assigned a value but never used.", "'clearGuideDetails' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'bannerButtonSelected', 'buttonColor', 'rtesContainer', 'setButtonColor', 'textAreas', and 'textBoxRef'. Either include them or remove the dependency array.", ["2939"], "React Hook useEffect has missing dependencies: 'buttonColor', 'removeTextArea', 'setButtonColor', and 'textAreas'. Either include them or remove the dependency array.", ["2940"], "React Hook useEffect has a missing dependency: 'setTextArray'. Either include it or remove the dependency array.", ["2941"], "React Hook useEffect has a missing dependency: 'textAreas'. Either include it or remove the dependency array.", ["2942"], "'setShowEmojiPicker' is assigned a value but never used.", "'enableProgress' is assigned a value but never used.", "'CustomIconButton' is defined but never used.", "'ArrowBackIosNewOutlinedIcon' is defined but never used.", "'parse' is defined but never used.", "'domToReact' is defined but never used.", "'Element' is defined but never used.", "'IconButtonSX' is defined but never used.", "'setShowBanner' is assigned a value but never used.", "'setImageSrc' is assigned a value but never used.", "'htmlContent' is assigned a value but never used.", "'Teext' is assigned a value but never used.", "'IconColor' is assigned a value but never used.", "'IconOpacity' is assigned a value but never used.", "'Width' is assigned a value but never used.", "'Radius' is assigned a value but never used.", "'Design' is assigned a value but never used.", "'brCount' is assigned a value but never used.", "'BannerEndUser' is defined but never used.", "'BannerStepPreview' is defined but never used.", "'setBannerPreview' is assigned a value but never used.", "'bannerPreview' is assigned a value but never used.", "'announcementPreview' is assigned a value but never used.", "'setAnnouncementPreview' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'OverlayValue'. Either include it or remove the dependency array. If 'setOverlayValue' needs the current value of 'OverlayValue', you can also switch to useReducer instead of useState and read 'OverlayValue' in the reducer.", ["2943"], "'imageStyle' is assigned a value but never used.", "'dissmissIconColor' is assigned a value but never used.", "'ActionButtonBackgroundcolor' is assigned a value but never used.", "'overlay' is assigned a value but never used.", "'openInNewTab' is assigned a value but never used.", "'HotspotGuideProps' is defined but never used.", "'hotspotGuideMetaData' is assigned a value but never used.", "valid-typeof", "Invalid typeof comparison value.", "invalidV<PERSON>ue", ["2944"], "React Hook useEffect has a missing dependency: 'getElementPosition'. Either include it or remove the dependency array.", ["2945"], "React Hook useEffect has a missing dependency: 'xpath'. Either include it or remove the dependency array.", ["2946"], "React Hook useEffect has a missing dependency: 'calculateOptimalWidth'. Either include it or remove the dependency array.", ["2947"], "React Hook useEffect has a missing dependency: 'guideStep'. Either include it or remove the dependency array.", ["2948"], "'hotspotData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'savedGuideData?.GuideStep', 'selectedTemplateTour', and 'setOpenTooltip'. Either include them or remove the dependency array.", ["2949"], ["2950"], "Assignments to the 'hotspot' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "'useEffect' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'count' is assigned a value but never used.", "'selectedStepStyle' is assigned a value but never used.", "'isSelected' is assigned a value but never used.", "'isHovered' is assigned a value but never used.", "'useRef' is defined but never used.", "'ClickAwayListener' is defined but never used.", "'LinearProgress' is defined but never used.", "'Breadcrumbs' is defined but never used.", "'updateCacheWithNewRows' is defined but never used.", "'toolTipGuideMetaData' is assigned a value but never used.", "no-unreachable", "Unreachable code.", "ReturnStatement", "unreachableCode", "'tooltip' is assigned a value but never used.", "'guideName' is assigned a value but never used.", "'borderRadius' is assigned a value but never used.", "'width' is assigned a value but never used.", "'tooltipXaxis' is assigned a value but never used.", "'tooltipYaxis' is assigned a value but never used.", "'tooltipWidth' is assigned a value but never used.", "'setTooltipWidth' is assigned a value but never used.", "'setTooltipPadding' is assigned a value but never used.", "'setTooltipBorderradius' is assigned a value but never used.", "'tooltipbordersize' is assigned a value but never used.", "'tooltipPosition' is assigned a value but never used.", "'setTooltipPosition' is assigned a value but never used.", "'selectedOption' is assigned a value but never used.", "'setCurrentStepIndex' is assigned a value but never used.", "'HotspotSettings' is assigned a value but never used.", "'currentGuideId' is assigned a value but never used.", "'setIsALTKeywordEnabled' is assigned a value but never used.", "'hoveredElement' is assigned a value but never used.", "'setHoveredElement' is assigned a value but never used.", "'overlayPosition' is assigned a value but never used.", "'setOverlayPosition' is assigned a value but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'currentElement'.", "ArrowFunctionExpression", "unsafeRefs", "'removeAppliedStyleOfEle' is assigned a value but never used.", "'isElementHover' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'applyCustomCursor', 'applyHotspotProperties', 'createWithAI', 'isCollapsed', 'isGuideInfoScreen', 'isTooltipNameScreenOpen', 'rectData', 'selectedTemplate', 'selectedTemplateTour', 'setAxisData', 'setCurrentHoveredElement', 'setElementSelected', 'setOpenTooltip', 'setTooltip', 'setXpathToTooltipMetaData', and 'syncAITooltipContainerData'. Either include them or remove the dependency array.", ["2951"], "React Hook useEffect has a missing dependency: 'applyCustomCursor'. Either include it or remove the dependency array.", ["2952"], "'normalizePx' is assigned a value but never used.", "'DotsStepper' is assigned a value but never used.", "'useState' is defined but never used.", "'ForkLeft' is defined but never used.", "'handleStepTypeChange' is assigned a value but never used.", "'Popover' is defined but never used.", "'CloseIcon' is defined but never used.", "'PopoverOrigin' is defined but never used.", "React Hook useEffect has missing dependencies: 'initializeTourHotspotMetadata', 'savedGuideData?.GuideStep', 'setAnnouncementPreview', 'setBannerPreview', 'setHotspotPreview', 'setOpenTooltip', and 'setTooltipPreview'. Either include them or remove the dependency array.", ["2953"], "'setCurrentUrl' is assigned a value but never used.", "Assignments to the 'savedGuideData' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "CallExpression", "'userApiService' is defined but never used.", "'AnySoaRecord' is defined but never used.", "'userUrl' is assigned a value but never used.", "'AxiosResponse' is defined but never used.", "'adminApiService' is defined but never used.", "'idsApiService' is defined but never used.", "'ArrowBackIosIcon' is defined but never used.", "'isUnSavedChanges' is assigned a value but never used.", "'openWarning' is assigned a value but never used.", "'setActiveMenu' is assigned a value but never used.", "'setSearchText' is assigned a value but never used.", "'setName' is assigned a value but never used.", "'handleKeyDown' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAnnouncements'. Either include it or remove the dependency array.", ["2954"], ["2955"], "'snackbarKey' is assigned a value but never used.", "'openSnackbar' is assigned a value but never used.", "'setImageLink' is assigned a value but never used.", "'handleLinkSubmit' is assigned a value but never used.", "'TextField' is defined but never used.", "'selectedtemp' is defined but never used.", "'contentState' is assigned a value but never used.", "React Hook React.useCallback has a missing dependency: 'isContentScrollable'. Either include it or remove the dependency array.", ["2956"], "React Hook useEffect has a missing dependency: 'setToolbarVisibleRTEId'. Either include it or remove the dependency array.", ["2957"], "The 'handlePaste' function makes the dependencies of useCallback Hook (at line 396) change on every render. Move it inside the useCallback callback. Alternatively, wrap the definition of 'handlePaste' in its own useCallback() Hook.", "'toggleToolbar' is assigned a value but never used.", "The 'containersToRender' array makes the dependencies of useEffect Hook (at line 450) change on every render. To fix this, wrap the initialization of 'containersToRender' in its own useMemo() Hook.", "'backgroundcoloricon' is defined but never used.", "'ButtonSettings' is defined but never used.", "'buttonProperty' is assigned a value but never used.", "'isEditingPrevious' is assigned a value but never used.", "'isEditingContinue' is assigned a value but never used.", "'previousButtonText' is assigned a value but never used.", "'continueButtonText' is assigned a value but never used.", "'buttonText' is assigned a value but never used.", "'setButtonText' is assigned a value but never used.", "'buttonToEdit' is assigned a value but never used.", "'isDeleteIcon' is assigned a value but never used.", "'isEditingButton' is assigned a value but never used.", "'isEditing' is assigned a value but never used.", "'setIsEditing' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setButtonProperty'. Either include it or remove the dependency array.", ["2958"], "'handlePreviousTextChange' is assigned a value but never used.", "'handleContinueTextChange' is assigned a value but never used.", "'toggleEdit' is assigned a value but never used.", "'handlePreviousBlur' is assigned a value but never used.", "'handleContinueBlur' is assigned a value but never used.", "'handleChangeButton' is assigned a value but never used.", "'handleEditButtonText' is assigned a value but never used.", "'LauncherSettings' is defined but never used.", "React Hook useEffect has missing dependencies: 'checkpointslistData' and 'completedStatus'. Either include them or remove the dependency array.", ["2959"], "React Hook useEffect has missing dependencies: 'createWithAI' and 'interactionData'. Either include them or remove the dependency array.", ["2960"], ["2961"], "React Hook useEffect has a missing dependency: 'checklistGuideMetaData'. Either include it or remove the dependency array.", ["2962"], "'toggleItemCompletion' is assigned a value but never used.", "'beta' is defined but never used.", "'setIsCollapsed' is assigned a value but never used.", "'accountId' is assigned a value but never used.", "'handleClick' is assigned a value but never used.", "'micicon' is defined but never used.", "'micicon_hover' is defined but never used.", "'PerfectScrollbar' is defined but never used.", "'isChatOpen' is assigned a value but never used.", "'setIsChatOpen' is assigned a value but never used.", "'isMicHovered' is assigned a value but never used.", "'setIsMicHovered' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'accountId' and 'openSnackbar'. Either include them or remove the dependency array.", ["2963"], "'handleSpeechRecognition' is assigned a value but never used.", "'isTourCreationPrompt' is assigned a value but never used.", "'parseTourSteps' is assigned a value but never used.", "'dataNew' is assigned a value but never used.", "'stepDataNew' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setElementSelected'. Either include it or remove the dependency array.", ["2964"], "'useReducer' is defined but never used.", "'SelectChangeEvent' is defined but never used.", "'Switch' is defined but never used.", "'ToggleButton' is defined but never used.", "'ToggleButtonGroup' is defined but never used.", "'BUTTON_CONT_DEF_VALUE_1' is defined but never used.", "'CANVAS_DEFAULT_VALUE' is defined but never used.", "'IMG_CONT_DEF_VALUE' is defined but never used.", "'HOTSPOT_DEFAULT_VALUE' is defined but never used.", "'InfoFilled' is defined but never used.", "'QuestionFill' is defined but never used.", "'Reselect' is defined but never used.", "'Solid' is defined but never used.", "'AddCircleOutlineIcon' is defined but never used.", "'InsertPhotoIcon' is defined but never used.", "'PersonIcon' is defined but never used.", "'FavoriteIcon' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'ErrorOutlineIcon' is defined but never used.", "'position' is defined but never used.", "'titlePopup' is assigned a value but never used.", "'setTitlePopup' is assigned a value but never used.", "'titleColor' is assigned a value but never used.", "'launcherColor' is assigned a value but never used.", "'hasChanges' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistGuideMetaData', 'checklistLauncherProperties', and 'icons'. Either include them or remove the dependency array.", ["2965"], "'handleTitleColorChange' is assigned a value but never used.", "'handledesignclose' is assigned a value but never used.", "'handleSizeChange' is assigned a value but never used.", "'onReselectElement' is assigned a value but never used.", "'handleIconColorChange' is assigned a value but never used.", "'handleLauncherColorChange' is assigned a value but never used.", "'type' is assigned a value but never used.", "'text' is assigned a value but never used.", "'setText' is assigned a value but never used.", "'textColor' is assigned a value but never used.", "'setTextColor' is assigned a value but never used.", "'icon' is assigned a value but never used.", "'appliedIconColorBase64Icon' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistLauncherProperties', 'icons', and 'updateChecklistLauncher'. Either include them or remove the dependency array.", ["2966"], "'setPositionLeft' is assigned a value but never used.", "'setSetPositionLeft' is assigned a value but never used.", "'deleteicon' is defined but never used.", "'deletestep' is defined but never used.", "'editpricol' is defined but never used.", "'getAllGuides' is defined but never used.", "'ShowLauncherSettings' is assigned a value but never used.", "'setTitleColor' is assigned a value but never used.", "'checkpointsPopup' is assigned a value but never used.", "'checkpointTitleColor' is assigned a value but never used.", "'setCheckpointTitleColor' is assigned a value but never used.", "'checkpointTitleDescription' is assigned a value but never used.", "'setCheckpointTitleDescription' is assigned a value but never used.", "'checkpointIconColor' is assigned a value but never used.", "'setCheckpointIconColor' is assigned a value but never used.", "'setUnlockCheckPointInOrder' is assigned a value but never used.", "'unlockCheckPointInOrder' is assigned a value but never used.", "'checkPointMessage' is assigned a value but never used.", "'setCheckPointMessage' is assigned a value but never used.", ["2967"], "'interactions' is assigned a value but never used.", "'setInteractions' is assigned a value but never used.", "'skip' is assigned a value but never used.", "'setSkip' is assigned a value but never used.", "'top' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "'dropdownRef' is assigned a value but never used.", "'RadioButtonUncheckedIcon' is defined but never used.", "'RadioButtonCheckedIcon' is defined but never used.", "'topCenter' is defined but never used.", "'OverlaySettingsProps' is defined but never used.", "'ElementsSettingsProps' is defined but never used.", "'setTooltipElementOptions' is assigned a value but never used.", "'updateprogressclick' is assigned a value but never used.", "'displayType' is assigned a value but never used.", "'dontShowAgain' is assigned a value but never used.", "'colors' is assigned a value but never used.", "'handleDisplayTypeChange' is assigned a value but never used.", "'handleBorderColorChange' is assigned a value but never used.", "'handleDontShowAgainChange' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dismissData.Color' and 'setDismiss'. Either include them or remove the dependency array.", ["2968"], "'defaultDots' is defined but never used.", "'topLeft' is defined but never used.", "'topRight' is defined but never used.", "'middleLeft' is defined but never used.", "'middleCenter' is defined but never used.", "'middleRight' is defined but never used.", "'bottomLeft' is defined but never used.", "'bottomMiddle' is defined but never used.", "'bottomRight' is defined but never used.", "'topcenter' is defined but never used.", "'setCanvasSetting' is assigned a value but never used.", "'announcement<PERSON>son' is assigned a value but never used.", "'setWidth' is assigned a value but never used.", "'setBorderRadius' is assigned a value but never used.", "'Annpadding' is assigned a value but never used.", "'setAnnPadding' is assigned a value but never used.", "'AnnborderSize' is assigned a value but never used.", "'setAnnBorderSize' is assigned a value but never used.", "'Bposition' is assigned a value but never used.", "'setBposition' is assigned a value but never used.", "'handleBackgroundColorChange' is assigned a value but never used.", "'checklistTitle' is assigned a value but never used.", "'setChecklistTitle' is assigned a value but never used.", "'checklistSubTitle' is assigned a value but never used.", "'setChecklistSubTitle' is assigned a value but never used.", "'setTempTitle' is assigned a value but never used.", "'settempTempTitle' is assigned a value but never used.", "'handleBlur' is assigned a value but never used.", "'setZindeex' is assigned a value but never used.", "'setOverlayEnabled' is assigned a value but never used.", "'handlePositionChange' is assigned a value but never used.", "'tempBorderSize' is assigned a value but never used.", "'setTempBorderSize' is assigned a value but never used.", "'tempZIndex' is assigned a value but never used.", "'setTempZIndex' is assigned a value but never used.", "'tempBorderColor' is assigned a value but never used.", "'setTempBorderColor' is assigned a value but never used.", "'tempBackgroundColor' is assigned a value but never used.", "'setTempBackgroundColor' is assigned a value but never used.", "'tempSectionColor' is assigned a value but never used.", "'setTempSectionColor' is assigned a value but never used.", "'Dialog' is defined but never used.", "'DialogContent' is defined but never used.", "'useMediaQuery' is defined but never used.", "'useTheme' is defined but never used.", "'zIndex' is defined but never used.", "'buttonsContainer' is assigned a value but never used.", "'cloneButtonContainer' is assigned a value but never used.", "'addNewButton' is assigned a value but never used.", "'deleteButton' is assigned a value but never used.", "'deleteButtonContainer' is assigned a value but never used.", "'updateContainer' is assigned a value but never used.", "'updateButtonInteraction' is assigned a value but never used.", "'setBtnBgColor' is assigned a value but never used.", "'setBtnTextColor' is assigned a value but never used.", "'setBtnBorderColor' is assigned a value but never used.", "'setBtnName' is assigned a value but never used.", "'setIsOpen' is assigned a value but never used.", "'selectedPosition' is assigned a value but never used.", "'url' is assigned a value but never used.", "'setUrl' is assigned a value but never used.", "'setAction' is assigned a value but never used.", "'setOpenInNewTab' is assigned a value but never used.", "'setColors' is assigned a value but never used.", "'buttonNameError' is assigned a value but never used.", "'setButtonNameError' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'defaultButtonColors.backgroundColor', 'defaultButtonColors.borderColor', 'defaultButtonColors.color', 'getCurrentButtonInfo', 'setCurrentButtonName', 'setSelectedTab', and 'setTargetURL'. Either include them or remove the dependency array.", ["2969"], "'positions' is assigned a value but never used.", "'curronButtonInfo' is assigned a value but never used.", "React Hook useMemo has missing dependencies: 'getCurrentButtonInfo' and 'setCurrentButtonName'. Either include them or remove the dependency array.", ["2970"], "'handlePositionClick' is assigned a value but never used.", "'imageContainerStyle' is assigned a value but never used.", "'iconRowStyle' is assigned a value but never used.", "'iconTextStyle' is assigned a value but never used.", "'setOpenTooltip' is assigned a value but never used.", "'setTooltipPositionByXpath' is assigned a value but never used.", "'updateTooltipBtnContainer' is assigned a value but never used.", "'updateTooltipImageContainer' is assigned a value but never used.", "'RefObject' is defined but never used.", "'CustomWidthTooltip' is defined but never used.", "'EXTENSION_PART' is defined but never used.", "'TOOLTIP_HEIGHT' is defined but never used.", "'TOOLTIP_MN_WIDTH' is defined but never used.", "'TOOLTIP_MX_WIDTH' is defined but never used.", "'Code' is defined but never used.", "'VideoLibrary' is defined but never used.", "'RTE' is defined but never used.", "'translate' is assigned a value but never used.", "'tooltipBackgroundcolor' is assigned a value but never used.", "'tooltipborderradius' is assigned a value but never used.", "'tooltipBordercolor' is assigned a value but never used.", "'tooltippadding' is assigned a value but never used.", "'elementClick' is assigned a value but never used.", "'setDismiss' is assigned a value but never used.", "'savedRange' is assigned a value but never used.", "'setSaveRange' is assigned a value but never used.", "'isEditorFocused' is assigned a value but never used.", "'setIsEditorFocused' is assigned a value but never used.", "'handleDeleteSection' is assigned a value but never used.", "'handleCloneContainer' is assigned a value but never used.", "'handleDragStart' is assigned a value but never used.", "'handleDragEnter' is assigned a value but never used.", "'handleDragEnd' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'popupPosition', 'setCurrentHoveredElement', 'setTooltipPositionByXpath', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["2971"], "'canvasProperties' is assigned a value but never used.", "'RTEToolbar' is assigned a value but never used.", "React Hook useMemo has a missing dependency: 'handleFocus'. Either include it or remove the dependency array.", ["2972"], "'isInsideJoditPopup' is assigned a value but never used.", "'isPasteEvent' is assigned a value but never used.", "'CustomImage' is defined but never used.", "'pageinteraction' is assigned a value but never used.", "React Hook useCallback has an unnecessary dependency: 'smoothScrollTo'. Either exclude it or remove the dependency array.", ["2973"], "React Hook useEffect has missing dependencies: 'currentStep' and 'selectedTemplate'. Either include them or remove the dependency array.", ["2974"], "React Hook useEffect has missing dependencies: 'currentStep' and 'currentStepIndex'. Either include them or remove the dependency array.", ["2975"], "React Hook useCallback has unnecessary dependencies: 'calculateBestPosition' and 'scrollToTargetElement'. Either exclude them or remove the dependency array.", ["2976"], "React Hook useCallback has a missing dependency: 'steps'. Either include it or remove the dependency array.", ["2977"], "React Hook useEffect has a missing dependency: 'currentStepIndex'. Either include it or remove the dependency array.", ["2978"], "React Hook useEffect has a missing dependency: 'updateTargetAndPosition'. Either include it or remove the dependency array.", ["2979"], ["2980"], ["2981"], "'hasOnlyTextContent' is assigned a value but never used.", "'hasOnlyButton' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE_HOTSPOT' is defined but never used.", "'TCanvas' is defined but never used.", "'updateDesignelementInTooltip' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE_HOTSPOT' is assigned a value but never used.", "'dismiss' is assigned a value but never used.", "'setSelectedPosition' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'selectedTemplate', 'selectedTemplateTour', 'setTooltipBackgroundcolor', 'setTooltipBordercolor', 'setTooltipBorderradius', 'setTooltipBordersize', 'setTooltipPadding', 'setTooltipPosition', 'setTooltipWidth', 'setTooltipXaxis', 'setTooltipYaxis', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["2982"], "'guideStatus' is assigned a value but never used.", "'RemoveIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'DriveFolderUploadIcon' is defined but never used.", "'BackupIcon' is defined but never used.", "'modifySVGColor' is assigned a value but never used.", "'setCheckPointsPopup' is assigned a value but never used.", "'handleCheckPointIconColorChange' is assigned a value but never used.", "'handleCheckPointTitleColorChange' is assigned a value but never used.", "'handleCheckPointDescriptionColorChange' is assigned a value but never used.", "'handleFileUpload' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["2983"], "'handleMenuScroll' is assigned a value but never used.", "'useCallback' is defined but never used.", "'checkpointsEditPopup' is assigned a value but never used.", "'updateChecklistCheckPoints' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'filteredInteractions'. Either include it or remove the dependency array.", ["2984"], "'applyclicked' is assigned a value but never used.", "'isSearching' is assigned a value but never used.", ["2985"], "'handleSearch' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistCheckpointListProperties' and 'icons'. Either include them or remove the dependency array.", ["2986"], "'handleColorChange' is assigned a value but never used.", "'FolderIcon' is defined but never used.", "'useAsyncError' is defined but never used.", "'getCurrentButtonInfo' is assigned a value but never used.", "'clickTimeout' is defined but never used.", "'handleEditButtonName' is assigned a value but never used.", "'Modal' is defined but never used.", "'IMG_EXPONENT' is defined but never used.", "'getAllFiles' is defined but never used.", "'selectedColor' is assigned a value but never used.", "'formOfUpload' is assigned a value but never used.", "'setFormOfUpload' is assigned a value but never used.", "'urll' is defined but never used.", "'handleHyperlinkClick' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setImageAnchorEl'. Either include it or remove the dependency array.", ["2987"], "'Image' is defined but never used.", "'Link' is defined but never used.", "'setIsUnSavedChanges' is assigned a value but never used.", "'setHtmlContent' is assigned a value but never used.", "'setTextvaluess' is assigned a value but never used.", "'setBackgroundC' is assigned a value but never used.", "'bpadding' is assigned a value but never used.", "'handleTooltipRTEBlur' is assigned a value but never used.", "React Hook useMemo has an unnecessary dependency: 'id'. Either exclude it or remove the dependency array.", ["2988"], "The 'updateContentState' function makes the dependencies of useCallback Hook (at line 270) change on every render. To fix this, wrap the definition of 'updateContentState' in its own useCallback() Hook.", ["2989"], "React Hook useEffect has a missing dependency: 'updateContentState'. Either include it or remove the dependency array.", ["2990"], "React Hook useEffect has a missing dependency: 'setToolbarVisibleRTEId'. Either include it or remove the dependency array. If 'setToolbarVisibleRTEId' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2991"], "'ColorResult' is defined but never used.", "'setBtnIdss' is assigned a value but never used.", "'gotoNextButtonId' is assigned a value but never used.", "'matchingButton' is assigned a value but never used.", ["2992"], "'getSavedLanguage' is assigned a value but never used.", "'saveLanguage' is assigned a value but never used.", "'clearSavedLanguage' is assigned a value but never used.", "'getAvailableLanguages' is defined but never used.", "React Hook useEffect has a missing dependency: 'userInfoObj'. Either include it or remove the dependency array.", ["2993"], "'orgId' is assigned a value but never used.", "'DialogContentText' is defined but never used.", "'TextareaAutosize' is defined but never used.", {"desc": "2994", "fix": "2995"}, {"desc": "2996", "fix": "2997"}, {"desc": "2998", "fix": "2999"}, {"desc": "3000", "fix": "3001"}, {"desc": "3002", "fix": "3003"}, {"desc": "3004", "fix": "3005"}, {"desc": "3006", "fix": "3007"}, {"desc": "3008", "fix": "3009"}, {"desc": "3010", "fix": "3011"}, {"desc": "3012", "fix": "3013"}, {"desc": "3014", "fix": "3015"}, {"desc": "3016", "fix": "3017"}, {"desc": "3018", "fix": "3019"}, {"desc": "3020", "fix": "3021"}, {"desc": "3022", "fix": "3023"}, {"desc": "3024", "fix": "3025"}, {"desc": "3026", "fix": "3027"}, {"desc": "3028", "fix": "3029"}, {"desc": "3030", "fix": "3031"}, {"messageId": "3032", "fix": "3033", "desc": "3034"}, {"messageId": "3035", "fix": "3036", "desc": "3037"}, {"desc": "3038", "fix": "3039"}, {"desc": "3040", "fix": "3041"}, {"desc": "3042", "fix": "3043"}, {"desc": "3044", "fix": "3045"}, {"desc": "3046", "fix": "3047"}, {"desc": "3048", "fix": "3049"}, {"desc": "3050", "fix": "3051"}, {"messageId": "3032", "fix": "3052", "desc": "3034"}, {"messageId": "3035", "fix": "3053", "desc": "3037"}, {"messageId": "3032", "fix": "3054", "desc": "3034"}, {"messageId": "3035", "fix": "3055", "desc": "3037"}, {"messageId": "3032", "fix": "3056", "desc": "3034"}, {"messageId": "3035", "fix": "3057", "desc": "3037"}, {"messageId": "3032", "fix": "3058", "desc": "3034"}, {"messageId": "3035", "fix": "3059", "desc": "3037"}, {"messageId": "3032", "fix": "3060", "desc": "3034"}, {"messageId": "3035", "fix": "3061", "desc": "3037"}, {"messageId": "3032", "fix": "3062", "desc": "3034"}, {"messageId": "3035", "fix": "3063", "desc": "3037"}, {"messageId": "3032", "fix": "3064", "desc": "3034"}, {"messageId": "3035", "fix": "3065", "desc": "3037"}, {"messageId": "3032", "fix": "3066", "desc": "3034"}, {"messageId": "3035", "fix": "3067", "desc": "3037"}, {"desc": "3068", "fix": "3069"}, {"desc": "3070", "fix": "3071"}, {"desc": "3072", "fix": "3073"}, {"desc": "3074", "fix": "3075"}, {"desc": "3076", "fix": "3077"}, {"desc": "3078", "fix": "3079"}, {"desc": "3080", "fix": "3081"}, {"desc": "3082", "fix": "3083"}, {"messageId": "3084", "data": "3085", "fix": "3086", "desc": "3087"}, {"desc": "3088", "fix": "3089"}, {"desc": "3090", "fix": "3091"}, {"desc": "3092", "fix": "3093"}, {"desc": "3094", "fix": "3095"}, {"desc": "3096", "fix": "3097"}, {"desc": "3098", "fix": "3099"}, {"desc": "3100", "fix": "3101"}, {"desc": "3102", "fix": "3103"}, {"desc": "3104", "fix": "3105"}, {"desc": "3106", "fix": "3107"}, {"desc": "3108", "fix": "3109"}, {"desc": "3110", "fix": "3111"}, {"desc": "3112", "fix": "3113"}, {"desc": "3114", "fix": "3115"}, {"desc": "3116", "fix": "3117"}, {"desc": "3118", "fix": "3119"}, {"desc": "3068", "fix": "3120"}, {"desc": "3121", "fix": "3122"}, {"desc": "3123", "fix": "3124"}, {"desc": "3125", "fix": "3126"}, {"desc": "3127", "fix": "3128"}, {"desc": "3129", "fix": "3130"}, {"desc": "3121", "fix": "3131"}, {"desc": "3132", "fix": "3133"}, {"desc": "3134", "fix": "3135"}, {"desc": "3136", "fix": "3137"}, {"desc": "3138", "fix": "3139"}, {"desc": "3140", "fix": "3141"}, {"desc": "3142", "fix": "3143"}, {"desc": "3144", "fix": "3145"}, {"desc": "3146", "fix": "3147"}, {"desc": "3148", "fix": "3149"}, {"desc": "3150", "fix": "3151"}, {"desc": "3152", "fix": "3153"}, {"desc": "3154", "fix": "3155"}, {"desc": "3154", "fix": "3156"}, {"desc": "3157", "fix": "3158"}, {"desc": "3159", "fix": "3160"}, {"desc": "3161", "fix": "3162"}, {"desc": "3163", "fix": "3164"}, {"desc": "3161", "fix": "3165"}, {"desc": "3166", "fix": "3167"}, {"desc": "3168", "fix": "3169"}, {"desc": "3170", "fix": "3171"}, {"desc": "3172", "fix": "3173"}, {"desc": "3174", "fix": "3175"}, {"desc": "3176", "fix": "3177"}, {"desc": "3178", "fix": "3179"}, {"desc": "3180", "fix": "3181"}, "Update the dependencies array to be: []", {"range": "3182", "text": "3183"}, "Update the dependencies array to be: [fetchGuideDetails, hotspot, hotspotClicked]", {"range": "3184", "text": "3185"}, "Update the dependencies array to be: [designPopup, setDesignPopup]", {"range": "3186", "text": "3187"}, "Update the dependencies array to be: [isTemplateScreen, isPopupOpen, bannerPopup, currentGuideId, selectedTemplate, selectedTemplateTour, determineCurrentScreen]", {"range": "3188", "text": "3189"}, "Update the dependencies array to be: [isCollapsed, isGuideInfoScreen, isTemplateScreen, isPopupOpen, bannerPopup, currentScreen, selectedTemplate, selectedTemplateTour, guideName, currentGuideId, guideStep, htmlContent, imageSrc, buttonColor, imageName, errors]", {"range": "3190", "text": "3191"}, "Update the dependencies array to be: [initialState, setIsAIGuidePersisted]", {"range": "3192", "text": "3193"}, "Update the dependencies array to be: [savedGuideData, isSaveInProgress, currentGuideId, selectedTemplate, selectedTemplateTour, bannerPopup, currentStep, setBannerPopup, setIsBannerCreationBuilderOpen, setIsAnnouncementCreationBuilderOpen, setIsTooltipCreationBuilderOpen, setIsHotspotCreationBuilderOpen, clearBannerButtonDetials, updateRTEContainerOnReload, setBannerButtonSelected, updateButtonContainerOnReload]", {"range": "3194", "text": "3195"}, "Update the dependencies array to be: [handleClose, selectedTemplate, selectedTemplateTour, isShowIcon, toolTipGuideMetaData, currentStep, elementSelected, resetALTKeywordForNewTooltip, setElementSelected, setIsALTKeywordEnabled]", {"range": "3196", "text": "3197"}, "Update the dependencies array to be: [openStepDropdown, plusIconclick, setSettingAnchorEl]", {"range": "3198", "text": "3199"}, "Update the dependencies array to be: [createWithAI, setIsUnSavedChanges, stepCreation]", {"range": "3200", "text": "3201"}, "Update the dependencies array to be: [isLoggedIn, organizationId, userType]", {"range": "3202", "text": "3203"}, "Update the dependencies array to be: [isAnnouncementPopupOpen, showBannerenduser, showTooltipenduser, showHotspotenduser, isTourTemplate]", {"range": "3204", "text": "3205"}, "Update the dependencies array to be: [currentGuide?.GuideStep, currentStep]", {"range": "3206", "text": "3207"}, "Update the dependencies array to be: [cleanupDuplicateSteps, createWithAI, currentGuideId, elementSelected, interactionData, resetHeightofBanner]", {"range": "3208", "text": "3209"}, "Update the dependencies array to be: [SetGuideName, cleanupDuplicateSteps, createWithAI, currentGuideId, currentStep, getGuideTypeValue, handlecreatefromScratchclick, interactionData, overlayEnabled, pageinteraction, progress, resetHeightofBanner, setBannerPopup, setBposition, setDismiss, setIsGuideInfoScreen, setOverlayEnabled, setPageInteraction, setProgress, setProgressColor, setSelectedOption, setSelectedTemplate, setSelectedTemplateTour, setTooltipCount, setTourDataOnEdit]", {"range": "3210", "text": "3211"}, "Update the dependencies array to be: [isAnnouncementPopupOpen, showHotspotenduser, isHotspotPopupOpen, isTourTemplate, isChecklistPreview, currentGuideId, setIsGuideInfoScreen, setSelectedTemplate, handlecreatefromScratchclick, SetGuideName, steps, selectedTemplate, getGuideTypeValue, currentStep]", {"range": "3212", "text": "3213"}, "Update the dependencies array to be: [isLoggedIn, setBannerPopup, setCreateWithAI, setCurrentGuideId, setIsAIGuidePersisted, setIsAnnouncementCreationBuilderOpen, setIsBannerCreationBuilderOpen, setIsGuideInfoScreen, setIsHomeScreen, setIsHotspotCreationBuilderOpen, setIsTemplateScreen, setIsTooltipCreationBuilderOpen, setIsTooltipPopup]", {"range": "3214", "text": "3215"}, "Update the dependencies array to be: [pendingWebTourModal, isTourPopupOpen, tourModalSource]", {"range": "3216", "text": "3217"}, "Update the dependencies array to be: [currentStep, deleteClicked, handleStepChange, steps, updateStepClicked]", {"range": "3218", "text": "3219"}, "removeEscape", {"range": "3220", "text": "3221"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "3222", "text": "3223"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", "Update the dependencies array to be: [loggedOut]", {"range": "3224", "text": "3225"}, "Update the dependencies array to be: [getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName, setBtnName]", {"range": "3226", "text": "3227"}, "Update the dependencies array to be: [settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, targetURL]", {"range": "3228", "text": "3229"}, "Update the dependencies array to be: [selectedActions.value, targetURL]", {"range": "3230", "text": "3231"}, "Update the dependencies array to be: [isExtensionClosed, setIsExtensionClosed]", {"range": "3232", "text": "3233"}, "Update the dependencies array to be: [hasAnnouncementOpened, setDrawerActiveMenu, setDrawerSearchText, setHasAnnouncementOpened]", {"range": "3234", "text": "3235"}, "Update the dependencies array to be: [isExtensionClosed, activeMenu, searchText, setDrawerActiveMenu, setDrawerSearchText, setIsPopupOpen]", {"range": "3236", "text": "3237"}, {"range": "3238", "text": "3221"}, {"range": "3239", "text": "3223"}, {"range": "3240", "text": "3221"}, {"range": "3241", "text": "3223"}, {"range": "3242", "text": "3221"}, {"range": "3243", "text": "3223"}, {"range": "3244", "text": "3221"}, {"range": "3245", "text": "3223"}, {"range": "3246", "text": "3221"}, {"range": "3247", "text": "3223"}, {"range": "3248", "text": "3221"}, {"range": "3249", "text": "3223"}, {"range": "3250", "text": "3221"}, {"range": "3251", "text": "3223"}, {"range": "3252", "text": "3221"}, {"range": "3253", "text": "3223"}, "Update the dependencies array to be: [checkpointslistData]", {"range": "3254", "text": "3255"}, "Update the dependencies array to be: [selectedTemplate, selectedTemplateTour, setHotspotPopup, setShowLauncherSettings, setShowTooltipCanvasSettings, setTitlePopup]", {"range": "3256", "text": "3257"}, "Update the dependencies array to be: [SetElementButtonClick, createWithAI, currentGuideId, currentStep, interactionData, setButtonClick, setDropdownValue, setElementButtonName, setElementClick, setbtnidss, toolTipGuideMetaData]", {"range": "3258", "text": "3259"}, "Update the dependencies array to be: [bannerButtonSelected, buttonColor, rtesContainer, setButtonColor, textAreas, textBoxRef]", {"range": "3260", "text": "3261"}, "Update the dependencies array to be: [bannerButtonSelected, buttonColor, removeTextArea, setButtonColor, textAreas]", {"range": "3262", "text": "3263"}, "Update the dependencies array to be: [setTextArray, textAreas]", {"range": "3264", "text": "3265"}, "Update the dependencies array to be: [createWithAI, bannerButtonSelected, buttonColor, setButtonColor, textAreas]", {"range": "3266", "text": "3267"}, "Update the dependencies array to be: [OverlayValue]", {"range": "3268", "text": "3269"}, "suggestString", {"type": "3270"}, {"range": "3271", "text": "3272"}, "Use `\"undefined\"` instead of `undefined`.", "Update the dependencies array to be: [getElementPosition, xpath]", {"range": "3273", "text": "3274"}, "Update the dependencies array to be: [savedGuideData, xpath]", {"range": "3275", "text": "3276"}, "Update the dependencies array to be: [textFieldProperties, imageProperties, customButton, currentStep, calculateOptimalWidth]", {"range": "3277", "text": "3278"}, "Update the dependencies array to be: [currentStep, guideStep, setOpenTooltip]", {"range": "3279", "text": "3280"}, "Update the dependencies array to be: [currentStep, isHotspotPopupOpen, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, toolTipGuideMetaData]", {"range": "3281", "text": "3282"}, "Update the dependencies array to be: [currentStep, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, showHotspotenduser, toolTipGuideMetaData]", {"range": "3283", "text": "3284"}, "Update the dependencies array to be: [toolTipGuideMetaData, elementSelected, borderSize, isALTKeywordEnabled, currentHoveredElement, rectData, selectedTemplate, selectedTemplateTour, isTooltipNameScreenOpen, isCollapsed, setCurrentHoveredElement, isGuideInfoScreen, setElementSelected, setAxisData, setTooltip, setOpenTooltip, setXpathToTooltipMetaData, createWithAI, syncAITooltipContainerData, applyHotspotProperties, applyCustomCursor]", {"range": "3285", "text": "3286"}, "Update the dependencies array to be: [isALTKeywordEnabled, elementSelected, selectedTemplate, selectedTemplateTour, applyCustomCursor]", {"range": "3287", "text": "3288"}, "Update the dependencies array to be: [stepType, currentStep, setAnnouncementPreview, setBannerPreview, setTooltipPreview, setHotspotPreview, savedGuideData?.GuideStep, setOpenTooltip, initializeTourHotspotMetadata]", {"range": "3289", "text": "3290"}, "Update the dependencies array to be: [paginationModel, activeTab, Open, accountId, fetchAnnouncements]", {"range": "3291", "text": "3292"}, "Update the dependencies array to be: [fetchAnnouncements, searchQuery]", {"range": "3293", "text": "3294"}, "Update the dependencies array to be: [isContentScrollable]", {"range": "3295", "text": "3296"}, "Update the dependencies array to be: [editingRTEId, setToolbarVisibleRTEId]", {"range": "3297", "text": "3298"}, "Update the dependencies array to be: [setButtonProperty]", {"range": "3299", "text": "3300"}, "Update the dependencies array to be: [checkpointslistData, completedStatus]", {"range": "3301", "text": "3302"}, "Update the dependencies array to be: [selectedItem, activeItem, createWithAI, interactionData]", {"range": "3303", "text": "3304"}, {"range": "3305", "text": "3255"}, "Update the dependencies array to be: [checklistGuideMetaData]", {"range": "3306", "text": "3307"}, "Update the dependencies array to be: [accountId, openSnackbar]", {"range": "3308", "text": "3309"}, "Update the dependencies array to be: [setElementSelected]", {"range": "3310", "text": "3311"}, "Update the dependencies array to be: [checklistGuideMetaData, checklistLauncherProperties, icons]", {"range": "3312", "text": "3313"}, "Update the dependencies array to be: [checklistLauncherProperties, icons, updateChecklistLauncher]", {"range": "3314", "text": "3315"}, {"range": "3316", "text": "3307"}, "Update the dependencies array to be: [dismissData.Color, dismissData?.dismisssel, setDismiss]", {"range": "3317", "text": "3318"}, "Update the dependencies array to be: [settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setCurrentButtonName, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, setSelectedTab]", {"range": "3319", "text": "3320"}, "Update the dependencies array to be: [getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName]", {"range": "3321", "text": "3322"}, "Update the dependencies array to be: [currentStep, openTooltip, popupPosition, setCurrentHoveredElement, setTooltipPositionByXpath, toolTipGuideMetaData]", {"range": "3323", "text": "3324"}, "Update the dependencies array to be: [handleFocus, isRtlDirection]", {"range": "3325", "text": "3326"}, "Update the dependencies array to be: [universalScrollTo]", {"range": "3327", "text": "3328"}, "Update the dependencies array to be: [currentStep, currentStepIndex, interactWithPage, selectedTemplate]", {"range": "3329", "text": "3330"}, "Update the dependencies array to be: [currentStep, currentStepData?.overlay, currentStepIndex, interactWithPage]", {"range": "3331", "text": "3332"}, "Update the dependencies array to be: [currentStepIndex, steps, selectedTemplate, currentStep, pollForElement, setCurrentStep, setCurrentStepIndex]", {"range": "3333", "text": "3334"}, "Update the dependencies array to be: [selectedTemplate, currentStepIndex, setCurrentStep, currentStep, steps]", {"range": "3335", "text": "3336"}, "Update the dependencies array to be: [currentStepData, currentStepIndex, handleNext]", {"range": "3337", "text": "3338"}, "Update the dependencies array to be: [currentStepData, currentUrl, updateTargetAndPosition]", {"range": "3339", "text": "3340"}, {"range": "3341", "text": "3340"}, "Update the dependencies array to be: [currentStepData, currentUrl, rect, updateTargetAndPosition]", {"range": "3342", "text": "3343"}, "Update the dependencies array to be: [currentStep, selectedTemplate, selectedTemplateTour, setTooltipBackgroundcolor, setTooltipBordercolor, setTooltipBorderradius, setTooltipBordersize, setTooltipPadding, setTooltipPosition, setTooltipWidth, setTooltipXaxis, setTooltipYaxis, toolTipGuideMetaData]", {"range": "3344", "text": "3345"}, "Update the dependencies array to be: [fetchData]", {"range": "3346", "text": "3347"}, "Update the dependencies array to be: [selectedInteraction, interactions, searchTerm, filteredInteractions]", {"range": "3348", "text": "3349"}, {"range": "3350", "text": "3347"}, "Update the dependencies array to be: [checklistCheckpointListProperties, icons]", {"range": "3351", "text": "3352"}, "Update the dependencies array to be: [setImageAnchorEl, tooltip.visible]", {"range": "3353", "text": "3354"}, "Update the dependencies array to be: [toolbarVisible]", {"range": "3355", "text": "3356"}, "Wrap the definition of 'updateContentState' in its own useCallback() Hook.", {"range": "3357", "text": "3358"}, "Update the dependencies array to be: [rteBoxValue, updateContentState]", {"range": "3359", "text": "3360"}, "Update the dependencies array to be: [isEditing, setToolbarVisibleRTEId]", {"range": "3361", "text": "3362"}, "Update the dependencies array to be: [settingAnchorEl.value, buttonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, btnidss, currentGuideId, currentStep, createWithAI, interactionData]", {"range": "3363", "text": "3364"}, "Update the dependencies array to be: [orgId, accessToken, userInfoObj]", {"range": "3365", "text": "3366"}, [18364, 18386], "[]", [26822, 26863], "[fetchGuideDetails, hotspot, hotspotClicked]", [27345, 27358], "[designPopup, setDesignPopup]", [30082, 30229], "[isTemplateScreen, isPopupOpen, bannerPopup, currentGuideId, selectedTemplate, selectedTemplateTour, determineCurrentScreen]", [30707, 31067], "[isCollapsed, isGuideInfoScreen, isTemplateScreen, isPopupOpen, bannerPopup, currentScreen, selectedTemplate, selectedTemplateTour, guideName, currentGuideId, guideStep, htmlContent, imageSrc, buttonColor, imageName, errors]", [31435, 31449], "[initialState, setIsAIGuidePersisted]", [35843, 35877], "[savedGuideData, isSaveInProgress, currentGuideId, selectedTemplate, selectedTemplateTour, bannerPopup, currentStep, setBannerPopup, setIsBannerCreationBuilderOpen, setIsAnnouncementCreationBuilderOpen, setIsTooltipCreationBuilderOpen, setIsHotspotCreationBuilderOpen, clearBannerButtonDetials, updateRTEContainerOnReload, setBannerButtonSelected, updateButtonContainerOnReload]", [71656, 71720], "[handleClose, selectedTemplate, selectedTemplateTour, isShowIcon, toolTipGuideMetaData, currentStep, elementSelected, resetALTKeywordForNewTooltip, setElementSelected, setIsALTKeywordEnabled]", [87763, 87796], "[openStepDropdown, plusIconclick, setSettingAnchorEl]", [102979, 102993], "[createWithAI, setIsUnSavedChanges, stepCreation]", [126751, 126779], "[isLoggedIn, organizationId, userType]", [133256, 133408], "[isAnnounce<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, showB<PERSON><PERSON><PERSON>er, showTooltipenduser, showHotspotenduser, isTourTemplate]", [163113, 163170], "[currentGuide?.GuideStep, currentStep]", [168426, 168443], "[cleanupDuplicateSteps, createWithAI, currentGuideId, elementSelected, interactionData, resetHeightofBanner]", [174376, 174409], "[SetGuideName, cleanupDuplicateSteps, createWithAI, currentGuideId, currentStep, getGuideTypeValue, handlecreatefromScratchclick, interactionData, overlayEnabled, pageinteraction, progress, resetHeightofBanner, setBannerPopup, setBposition, setDismiss, setIsGuideInfoScreen, setOverlayEnabled, setPageInteraction, setProgress, setProgressColor, setSelectedOption, setSelectedTemplate, setSelectedTemplateTour, setTooltipCount, setTourDataOnEdit]", [175119, 175220], "[isAnnouncementPopupOpen, showHotspotenduser, isHotspotPopupOpen, isTourTemplate, isChecklistPreview, currentGuideId, setIsGuideInfoScreen, setSelectedTemplate, handlecreatefromScratchclick, SetGuideName, steps, selectedTemplate, getGuideTypeValue, currentStep]", [180650, 180662], "[isLoggedIn, setBannerPopup, setCreateWithAI, setCurrentGuideId, setIsAIGuidePersisted, setIsAnnouncementCreationBuilderOpen, setIsBannerCreationBuilderOpen, setIsGuideInfoScreen, setIsHomeScreen, setIsHotspotCreationBuilderOpen, setIsTemplateScreen, setIsTooltipCreationBuilderOpen, setIsTooltipPopup]", [181082, 181155], "[pendingWebTourModal, isTourPopupOpen, tourModalSource]", [181724, 181731], "[currentStep, deleteClicked, handleStepChange, steps, updateStepClicked]", [203879, 203880], "", [203879, 203879], "\\", [4501, 4503], "[loggedOut]", [16287, 16342], "[getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName, setBtnName]", [17716, 17771], "[settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, targetURL]", [18216, 18218], "[selectedActions.value, targetURL]", [2420, 2422], "[isExtensionClosed, setIsExtensionClosed]", [3087, 3110], "[hasAnnouncementOpened, setDrawerActiveMenu, setDrawerSearchText, setHasAnnouncementOpened]", [3454, 3497], "[isExtensionClosed, activeMenu, searchText, setDrawerActiveMenu, setDrawerSearchText, setIsPopupOpen]", [22521, 22522], [22521, 22521], [22525, 22526], [22525, 22525], [22536, 22537], [22536, 22536], [22540, 22541], [22540, 22540], [22569, 22570], [22569, 22569], [22573, 22574], [22573, 22573], [22584, 22585], [22584, 22584], [22588, 22589], [22588, 22588], [5960, 5993], "[checkpointslistData]", [5310, 5350], "[selectedTemplate, selectedTemplateTour, setHotspotPopup, setShowLauncherSettings, setShowTooltipCanvasSettings, setTitlePopup]", [10234, 10269], "[SetElementButtonClick, createWithAI, currentGuideId, currentStep, interactionData, setButtonClick, setDropdownValue, setElementButtonName, setElementClick, setbtnidss, toolTipGuideMetaData]", [6153, 6155], "[bannerButtonSelected, buttonColor, rtesContainer, setButtonColor, textAreas, textBoxRef]", [7218, 7240], "[bannerButtonSelected, buttonColor, removeTextArea, setButtonColor, textAreas]", [7401, 7412], "[setTex<PERSON><PERSON><PERSON><PERSON>, text<PERSON><PERSON>s]", [8963, 9028], "[createWithAI, bannerButtonSelected, buttonColor, setButtonColor, textAreas]", [4728, 4730], "[OverlayValue]", "undefined", [6583, 6592], "\"undefined\"", [6719, 6726], "[getElementPosition, xpath]", [6855, 6871], "[savedGuideData, xpath]", [14985, 15050], "[textFieldProperties, imageProperties, customButton, currentStep, calculateOptimalWidth]", [19065, 19124], "[currentStep, guideStep, setOpenTooltip]", [19978, 20020], "[currentStep, isHotspotPopupOpen, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, toolTipGuideMetaData]", [20812, 20854], "[currentStep, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, showHotspotenduser, toolTipGuideMetaData]", [25153, 25248], "[toolTipGuideMetaData, elementSelected, borderSize, isALTKeywordEnabled, currentHoveredElement, rectData, selectedTemplate, selectedTemplateTour, isTooltipNameScreenOpen, isCollapsed, setCurrentHoveredElement, isGuideInfoScreen, setElementSelected, setAxisData, setTooltip, setOpenTooltip, setXpathToTooltipMetaData, createWithAI, syncAITooltipContainerData, applyHotspotProperties, applyCustomCursor]", [25832, 25910], "[isALTKeywordEnabled, elementSelected, selectedTemplate, selectedTemplateTour, applyCustomCursor]", [6220, 6243], "[stepType, currentStep, setAnnouncementPreview, setBannerPreview, setTooltipPreview, setHotspotPreview, savedGuideData?.GuideStep, setOpenTooltip, initializeTourHotspotMetadata]", [8915, 8960], "[paginationModel, activeTab, Open, accountId, fetchAnnouncements]", [9266, 9279], "[fetchAnnouncements, searchQuery]", [4465, 4467], "[isContentScrollable]", [7277, 7291], "[editingRTEId, setToolbarVisibleRTEId]", [2651, 2653], "[setButtonProperty]", [3543, 3545], "[checkpointslistData, completedStatus]", [4387, 4413], "[selectedItem, activeItem, createWithAI, interactionData]", [4517, 4551], [5507, 5534], "[checklistGuideMetaData]", [4604, 4606], "[accountId, openSnackbar]", [17421, 17423], "[setElementSelected]", [4872, 4874], "[checklistGuideMetaData, checklistLauncherProperties, icons]", [9650, 9652], "[checklistLauncherProperties, icons, updateChecklistLauncher]", [3211, 3251], [6878, 6903], "[dismissData.Color, dismissData?.dismisssel, setDismiss]", [4631, 4686], "[settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setCurrentButtonName, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, setSelectedTab]", [6164, 6219], "[getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName]", [10303, 10316], "[currentStep, openTooltip, popupPosition, setCurrentHoveredElement, setTooltipPositionByXpath, toolTipGuideMetaData]", [26651, 26667], "[handleFocus, isRtlDirection]", [19204, 19239], "[universalScrollTo]", [20357, 20393], "[currentStep, currentStepIndex, interactWithPage, selectedTemplate]", [20804, 20848], "[currentStep, currentStepData?.overlay, currentStepIndex, interactWithPage]", [29926, 30081], "[currentStepIndex, steps, selectedTemplate, currentStep, pollForElement, setCurrentStep, setCurrentStepIndex]", [32166, 32271], "[selectedTemplate, currentStepIndex, setCurrentStep, currentStep, steps]", [33472, 33501], "[currentStepData, currentStepIndex, handleNext]", [33978, 34007], "[currentStepData, currentUrl, updateTargetAndPosition]", [34419, 34448], [34507, 34542], "[currentStepData, currentUrl, rect, updateTargetAndPosition]", [7898, 7945], "[currentStep, selectedTemplate, selectedTemplateTour, setTooltipBackgroundcolor, setTooltipBordercolor, setTooltipBorderradius, setTooltipBordersize, setTooltipPadding, setTooltipPosition, setTooltipWidth, setTooltipXaxis, setTooltipYaxis, toolTipGuideMetaData]", [7680, 7682], "[fetchData]", [4285, 4332], "[selectedInteraction, interactions, searchTerm, filteredInteractions]", [6555, 6557], [12538, 12540], "[checklistCheckpointListProperties, icons]", [10130, 10147], "[setImageAnchorEl, tooltip.visible]", [5515, 5535], "[toolbarVisible]", [6318, 6488], "useCallback((content: string) => {\r\n\t\t\tconst isEmpty = isContentEmpty(content);\r\n\t\t\tconst isScrollable = isContentScrollable();\r\n\r\n\t\t\tsetContentState({ isEmpty, isScrollable });\r\n\t\t})", [9064, 9077], "[rteBoxValue, updateContentState]", [12367, 12378], "[isEditing, setToolbarVisibleRTEId]", [4267, 4417], "[settingAnchorEl.value, buttonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, btnidss, currentGuideId, currentStep, createWithAI, interactionData]", [4596, 4616], "[orgId, accessToken, userInfoObj]"]